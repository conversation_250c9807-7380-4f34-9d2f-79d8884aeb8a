volumes:
  membership_postgres_data:

networks:
  membership_network:

services:
  membership_service:
    image: node:latest
    working_dir: /usr/app
    volumes:
      - ./:/usr/app
    ports:
      - 3000:3000
      - 9229:9229
    env_file: .env
    depends_on:
      - membership_postgres
    command: sh -c "sleep 5 && npm run migrate:latest && npm run nodemon"
    networks:
      - membership_network

  membership_postgres:
    image: postgres
    environment:
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev@123
    volumes:
      - membership_postgres_data:/var/lib/postgresql/data
    ports:
      - 5433:5432
    networks:
      - membership_network
