// migrations/20230426120000_create_memberships_table.js
const SCHEMA = "membership";
const TABLE_NAME = "tbl_memberships";

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
    table.uuid("id").primary();
    table.string("tenant_id", 50).nullable();
    table.string("name").notNullable();
    table.string("status").notNullable(); // status of membership
    table.boolean("is_custom").notNullable();
    table.boolean("is_trial").notNullable();
    table.string("description").nullable();
    table.timestamp("created_at", { useTz: true }).notNullable();
    table.string("created_by").notNullable();
    table.timestamp("updated_at", { useTz: true }).nullable();
    table.string("updated_by").nullable();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  // return knex.schema.withSchema(SCHEMA).dropTableIfExists(TABLE_NAME);
};
