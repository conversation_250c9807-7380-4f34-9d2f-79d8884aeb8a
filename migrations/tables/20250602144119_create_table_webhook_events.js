const SCHEMA = "membership";
const TABLE_NAME = "tbl_webhook_events";

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
    table.uuid("id").primary();
    table.string("gateway", 100).notNullable();
    table.string("gateway_event_id").notNullable();
    table.string("event_type").notNullable();
    table.string("status", 20).notNullable();
    table.jsonb("payload").nullable();
    table.timestamp("received_at", { useTz: true }).notNullable();
    table.timestamp("processed_at", { useTz: true }).nullable();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
