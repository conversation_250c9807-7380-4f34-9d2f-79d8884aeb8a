const SCHEMA = "membership";
const TABLE_NAME = "tbl_invoices";

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
    table.uuid("id").primary();
    table.uuid("subscription_id").notNullable();
    table.uuid("payment_method_id").nullable();
    table.bigInteger("period_start").nullable();
    table.bigInteger("period_end").nullable();
    table.string("status", 20).notNullable();
    table.string("invoice_gateway").notNullable();
    table.timestamp("created_at", { useTz: true }).notNullable();
    table.timestamp("updated_at", { useTz: true }).nullable();

    // Foreign key
    table.foreign("subscription_id").references("id").inTable(`${SCHEMA}.tbl_subscriptions`);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
