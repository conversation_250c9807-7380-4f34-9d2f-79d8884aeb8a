const SCHEMA = "membership";
const TABLE_NAME = "tbl_capabilities";

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
    table.uuid("id").primary();
    table.string("key").notNullable().unique();
    table.string("description").notNullable();
    table.timestamp("created_at", { useTz: true }).notNullable();
    table.timestamp("updated_at", { useTz: true }).nullable();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
