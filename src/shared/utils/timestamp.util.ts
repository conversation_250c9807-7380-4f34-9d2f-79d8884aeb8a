export class TimestampUtil {
  static convertSecToMs(timestamp: number | string): number {
    const value = typeof timestamp === "string" ? Number(timestamp) : timestamp;

    if (!Number.isFinite(value)) {
      throw new Error("Invalid timestamp: not a number");
    }

    const digitCount = Math.floor(Math.log10(value)) + 1;

    if (digitCount === 10) {
      return value * 1000;
    }

    if (digitCount === 13) {
      return value;
    }

    throw new Error(`Unexpected timestamp format (digits: ${digitCount})`);
  }
}
