import "reflect-metadata";
import { HeronJS } from "@heronjs/core";
import { PostgresDataSource } from "./data-sources";
import { AuthContext } from "./context/auth.context";
import { GlobalErrorInterceptor } from "./interceptors";
import { MembershipModule } from "./features/membership.module";
import { DataSources, GateKeeper, Module } from "@heronjs/common";
// import { ContextInterceptor } from "src/interceptors/global-context.interceptor";

@Module({
  imports: [MembershipModule],
})
@GateKeeper(AuthContext, AuthContext.Resolver)
@DataSources([PostgresDataSource])
export class AppModule {}

const main = async () => {
  const app = await HeronJS.create({ module: AppModule });
  await app.listen({
    port: 3000,
    options: {
      cors: {
        origin: "*",
        preflightContinue: false,
        methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
      },
      globalError: GlobalErrorInterceptor,
      // interceptors: [ContextInterceptor],
    },
  });
};

(async () => main())();
