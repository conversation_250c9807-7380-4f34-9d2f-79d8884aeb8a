import { PaymentGatewayProvider } from "@features/domain";

function isProviderSupported(code?: string) {
  if (!code) return null;
  const isProviderSupported = Object.values(PaymentGatewayProvider)
    .map((value) => value.toString())
    .includes(code);
  if (!isProviderSupported) return null;
  return code as PaymentGatewayProvider;
}

export const PAYMENT_GATEWAY_CONFIG = {
  PROVIDER:
    isProviderSupported(process.env.PAYMENT_GATEWAY_PROVIDER) ?? PaymentGatewayProvider.STRIPE,
  SECRET_KEY: {
    STRIPE: process.env.STRIPE_SECRET_KEY ?? "",
  },
  WEBHOOK_SECRET: {
    STRIPE: process.env.STRIPE_WEBHOOK_SECRET ?? "",
  },
  API_VERSION: {
    STRIPE: "2025-04-30.basil",
  },
};
