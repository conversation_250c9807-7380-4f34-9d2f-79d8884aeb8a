export const MEMBERSHIP_INJECT_TOKENS = {
  REPOSITORY: {
    MEMBERSHIP: Symbol("MEMBERSHIP_REPOSITORY").toString(),
    CAPABILITY: Symbol("CAPABILITY_REPOSITORY").toString(),
    SUBSCRIPTION: Symbol("SUBSCRIPTION_REPOSITORY").toString(),
    WEBHOOK_EVENT: Symbol("WEBHOOK_EVENT_REPOSITORY").toString(),
    MEMBERSHIP_CAPABILITY: Symbol("MEMBERSHIP_CAPABILITY_REPOSITORY").toString(),
  },
  BUILDER: {
    MEMBERSHIP: Symbol("MEMBERSHIP_BUILDER").toString(),
    SUBSCRIPTION: Symbol("SUBSCRIPTION_BUILDER").toString(),
    CAPABILITY: Symbol("CAPABILITY_BUILDER").toString(),
    WEBHOOK_EVENT: Symbol("WEBHOOK_EVENT_BUILDER").toString(),
  },
  USECASE: {
    // Membership
    CREATE_MEMBERSHIP: Symbol("CREATE_MEMBERSHIP_USECASE").toString(),
    UPDATE_MEMBERSHIP: Symbol("UPDATE_MEMBERSHIP_USECASE").toString(),
    DELETE_MEMBERSHIP: Symbol("DELETE_MEMBERSHIP_USECASE").toString(),
    GET_MEMBERSHIP_BY_ID: Symbol("GET_MEMBERSHIP_BY_ID_USECASE").toString(),
    GET_LIST_OF_MEMBERSHIPS: Symbol("GET_LIST_OF_MEMBERSHIPS_USECASE").toString(),
    ADD_MEMBERSHIP_CYCLE: Symbol("ADD_MEMBERSHIP_CYCLE_USECASE").toString(),

    // Capability
    CREATE_CAPABILITY: Symbol("CREATE_CAPABILITY_USECASE").toString(),
    GET_LIST_OF_CAPABILITIES: Symbol("GET_LIST_OF_CAPABILITIES_USECASE").toString(),
    GET_SUBSCRIPTION: Symbol("GET_SUBSCRIPTION_USECASE").toString(),

    // Membership Capability
    ASSIGN_CAPABILITY_TO_MEMBERSHIP: Symbol("ASSIGN_CAPABILITY_TO_MEMBERSHIP_USECASE").toString(),
    REMOVE_CAPABILITY_FROM_MEMBERSHIP: Symbol(
      "REMOVE_CAPABILITY_FROM_MEMBERSHIP_USECASE",
    ).toString(),
    GET_MEMBERSHIP_CAPABILITIES: Symbol("GET_MEMBERSHIP_CAPABILITIES_USECASE").toString(),

    // Subscription
    ASSIGN_SUBSCRIPTION: Symbol("ASSIGN_SUBSCRIPTION_USECASE").toString(),
    EXTEND_SUBSCRIPTION_TRIAL_PERIOD: Symbol("EXTEND_SUBSCRIPTION_TRIAL_PERIOD_USECASE").toString(),
    UPDATE_SUBSCRIPTION: Symbol("UPDATE_SUBSCRIPTION_USECASE").toString(),
    CANCEL_SUBSCRIPTION: Symbol("CANCEL_SUBSCRIPTION_USECASE").toString(),
    EXPIRE_SUBSCRIPTION: Symbol("EXPIRE_SUBSCRIPTION_USECASE").toString(),
    GET_SUBSCRIPTION_BY_ID: Symbol("GET_SUBSCRIPTION_BY_ID_USECASE").toString(),
    GET_SUBSCRIPTION_BY_TENANT_ID: Symbol("GET_SUBSCRIPTION_BY_TENANT_ID_USECASE").toString(),
    ACTIVATE_SUBSCRIPTION: Symbol("ACTIVATE_SUBSCRIPTION_USECASE").toString(),
    GET_CURRENT_SUBSCRIPTION: Symbol("GET_CURRENT_SUBSCRIPTION_USECASE").toString(),

    // Notifications
    NOTIFY_SUBSCRIPTION_EXPIRY: Symbol("NOTIFY_SUBSCRIPTION_EXPIRY_USECASE").toString(),

    // Payment Gateway
    HANDLE_WEBHOOK_EVENT: Symbol("HANDLE_WEBHOOK_EVENT_USECASE").toString(),
  },
  DAO: {
    MEMBERSHIP: Symbol("MEMBERSHIP_DAO").toString(),
    MEMBERSHIP_CYCLE: Symbol("MEMBERSHIP_CYCLE_DAO").toString(),
    SUBSCRIPTION: Symbol("SUBSCRIPTION_DAO").toString(),
    INVOICE: Symbol("INVOICE_DAO").toString(),
    MEMBERSHIP_GATEWAY_MAPPING: Symbol("MEMBERSHIP_GATEWAY_MAPPING_DAO").toString(),
    MEMBERSHIP_CYCLE_GATEWAY_MAPPING: Symbol("MEMBERSHIP_CYCLE_GATEWAY_MAPPING_DAO").toString(),
    SUBSCRIPTION_GATEWAY_MAPPING: Symbol("SUBSCRIPTION_GATEWAY_MAPPING_DAO").toString(),
    CAPABILITY: Symbol("CAPABILITY_DAO").toString(),
    WEBHOOK_EVENT: Symbol("WEBHOOK_EVENT_DAO").toString(),
    MEMBERSHIP_CAPABILITY: Symbol("MEMBERSHIP_CAPABILITY_DAO").toString(),
  },
  MAPPER: {
    INVOICE: Symbol("INVOICE_MAPPER").toString(),
    MEMBERSHIP: Symbol("MEMBERSHIP_MAPPER").toString(),
    MEMBERSHIP_GATEWAY_MAPPING: Symbol("MEMBERSHIP_GATEWAY_MAPPING_MAPPER").toString(),
    MEMBERSHIP_CYCLE: Symbol("MEMBERSHIP_CYCLE_MAPPER").toString(),
    MEMBERSHIP_CYCLE_GATEWAY_MAPPING: Symbol("MEMBERSHIP_CYCLE_GATEWAY_MAPPING_MAPPER").toString(),
    SUBSCRIPTION: Symbol("SUBSCRIPTION_MAPPER").toString(),
    SUBSCRIPTION_GATEWAY_MAPPING: Symbol("SUBSCRIPTION_GATEWAY_MAPPING_MAPPER").toString(),
    CAPABILITY: Symbol("CAPABILITY_MAPPER").toString(),
    MEMBERSHIP_CAPABILITY: Symbol("MEMBERSHIP_CAPABILITY_MAPPER").toString(),
    WEBHOOK_EVENT: Symbol("WEBHOOK_EVENT_MAPPER").toString(),
  },
  SERVICE: {
    NOTIFICATION: Symbol("NOTIFICATION_SERVICE").toString(),
    USER_PROFILE: Symbol("USER_PROFILE_SERVICE").toString(),
    PAYMENT: Symbol("PAYMENT_SERVICE").toString(),
  },
  FACTORY: {
    PAYMENT_GATEWAY: Symbol("PAYMENT_GATEWAY_FACTORY").toString(),
    WEBHOOK_HANDLER: Symbol("WEBHOOK_HANDLER_FACTORY").toString(),
  },
  UTIL: {
    RETRY: Symbol("RETRY_UTIL").toString(),
    DATABASE: Symbol("DATABASE_UTIL").toString(),
  },
  HANDLER: {
    PAYMENT_SUCCEEDED: Symbol("PAYMENT_SUCCEEDED_HANDLER").toString(),
    PAYMENT_FAILED: Symbol("PAYMENT_FAILED_HANDLER").toString(),
    SUBSCRIPTION_CANCELLED: Symbol("SUBSCRIPTION_CANCELLED_HANDLER").toString(),
    INVOICE_CREATED: Symbol("INVOICE_CREATED_HANDLER").toString(),
  },

  PAYMENT_GATEWAY: {
    STRIPE: Symbol("STRIPE_PAYMENT_GATEWAY").toString(),
    PAYPAL: Symbol("PAYPAL_PAYMENT_GATEWAY").toString(),
  },
};
