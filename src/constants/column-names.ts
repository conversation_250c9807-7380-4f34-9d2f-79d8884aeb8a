export const MEMBERSHIP_MODULE_COLUMN_NAMES = {
  MEMBERSHIP: {
    ID: "id",
    NAME: "name",
    STATUS: "status",
    TENANT_ID: "tenant_id",
    IS_CUSTOM: "is_custom",
    IS_TRIAL: "is_trial",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
    DESCRIPTION: "description",
    CREATED_BY: "created_by",
    UPDATED_BY: "updated_by",
  },
  MEMBERSHIP_CYCLE: {
    ID: "id",
    MEMBERSHIP_ID: "membership_id",
    BILLING_CYCLE: "billing_cycle",
    AMOUNT: "amount",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
  },
  SUBSCRIPTION: {
    ID: "id",
    TENANT_ID: "tenant_id",
    MEMBERSHIP_ID: "membership_id",
    MEMBERSHIP_CYCLE_ID: "membership_cycle_id",
    STATUS: "status",
    GATEWAY: "gateway",
    TRIAL_END: "trial_end",
    CURRENT_PERIOD_END: "current_period_end",
    CREATED_AT: "created_at",
    CREATED_BY: "created_by",
    UPDATED_AT: "updated_at",
    UPDATED_BY: "updated_by",
  },
  INVOICE: {
    ID: "id",
    STATUS: "status",
    SUBSCRIPTION_ID: "subscription_id",
    PERIOD_START: "period_start",
    PERIOD_END: "period_end",
    INVOICE_GATEWAY: "invoice_gateway",
    AMOUNT: "amount",
    PAYMENT_METHOD_ID: "payment_method_id",
  },
  CAPABILITY: {
    ID: "id",
    KEY: "key",
    DESCRIPTION: "description",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
  },
  MEMBERSHIP_CAPABILITY: {
    ID: "id",
    MEMBERSHIP_ID: "membership_id",
    CAPABILITY_ID: "capability_id",
    VALUE: "value",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
  },
  SUBSCRIPTION_GATEWAY_MAPPING: {
    ID: "id",
    SUBSCRIPTION_ID: "subscription_id",
    SUBSCRIPTION_GATEWAY_ID: "subscription_gateway_id",
    GATEWAY: "gateway",
    CREATED_AT: "created_at",
    CREATED_BY: "created_by",
    UPDATED_AT: "updated_at",
    UPDATED_BY: "updated_by",
  },
  MEMBERSHIP_GATEWAY_MAPPING: {
    ID: "id",
    MEMBERSHIP_ID: "membership_id",
    MEMBERSHIP_GATEWAY_ID: "membership_gateway_id",
    GATEWAY: "gateway",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
  },
  MEMBERSHIP_CYCLE_GATEWAY_MAPPING: {
    ID: "id",
    MEMBERSHIP_CYCLE_ID: "membership_cycle_id",
    MEMBERSHIP_CYCLE_GATEWAY_ID: "membership_cycle_gateway_id",
    GATEWAY: "gateway",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
  },
};
