import { Module } from "@heronjs/common";
import {
  PaymentF<PERSON><PERSON>andler,
  InvoiceCreated<PERSON><PERSON><PERSON>,
  WebhookHandlerFactory,
  PaymentSucceededHandler,
  CreateMembershipUseCase,
  UpdateMembershipUseCase,
  CreateCapabilityUseCase,
  GetMembershipByIdUseCase,
  CancelSubscriptionUseCase,
  AssignSubscriptionUseCase,
  ActivateSubscriptionUseCase,
  GetListOfMembershipsUseCase,
  GetListOfCapabilitiesUseCase,
  SubscriptionCancelledHandler,
  GetCurrentSubscriptionUseCase,
  GetMembershipCapabilitiesUseCase,
  ExtendSubscriptionTrialPeriodUseCase,
} from "@features/app";
import {
  PaymentWebhook,
  AdminCapabilityRest,
  AdminMembershipRest,
  SubscriptionRest,
  AdminSubscriptionRest,
  InternalSubscriptionRest,
} from "@features/presentation";
import {
  InvoiceMapper,
  CapabilityMapper,
  MembershipMapper,
  CapabilityBuilder,
  MembershipBuilder,
  SubscriptionMapper,
  WebhookEventMapper,
  SubscriptionBuilder,
  MembershipCycleMapper,
  MembershipCapabilityMapper,
  MembershipGatewayMappingMapper,
  SubscriptionGatewayMappingMapper,
  MembershipCycleGatewayMappingMapper,
} from "@features/domain";
import {
  InvoiceDao,
  DatabaseUtil,
  CapabilityDao,
  MembershipDao,
  PaymentService,
  BasicRetryUtil,
  SubscriptionDao,
  WebhookEventDao,
  MembershipCycleDao,
  UserProfileService,
  MembershipRepository,
  PaypalPaymentGateway,
  StripePaymentGateway,
  CapabilityRepository,
  PaymentGatewayFactory,
  SubscriptionRepository,
  WebhookEventRepository,
  MembershipCapabilityDao,
  MembershipGatewayMappingDao,
  SubscriptionGatewayMappingDao,
  MembershipCycleGatewayMappingDao,
} from "@features/infra";

@Module({
  controllers: [
    AdminMembershipRest,
    AdminSubscriptionRest,
    AdminCapabilityRest,
    InternalSubscriptionRest,
    SubscriptionRest,
    PaymentWebhook,
  ],
  providers: [
    // Mappers
    InvoiceMapper,
    CapabilityMapper,
    MembershipMapper,
    SubscriptionMapper,
    WebhookEventMapper,
    MembershipCycleMapper,
    MembershipCapabilityMapper,
    MembershipGatewayMappingMapper,
    MembershipCycleGatewayMappingMapper,
    SubscriptionGatewayMappingMapper,

    // Builders
    CapabilityBuilder,
    MembershipBuilder,
    SubscriptionBuilder,

    // DAOs
    InvoiceDao,
    CapabilityDao,
    MembershipDao,
    WebhookEventDao,
    SubscriptionDao,
    MembershipCycleDao,
    MembershipCapabilityDao,
    MembershipGatewayMappingDao,
    SubscriptionGatewayMappingDao,
    MembershipCycleGatewayMappingDao,

    // Repositories
    CapabilityRepository,
    MembershipRepository,
    WebhookEventRepository,
    SubscriptionRepository,

    // Membership Use Cases
    CreateMembershipUseCase,
    UpdateMembershipUseCase,
    GetMembershipByIdUseCase,
    GetListOfMembershipsUseCase,

    // Tenant Membership Use Cases
    AssignSubscriptionUseCase,
    CancelSubscriptionUseCase,
    ActivateSubscriptionUseCase,
    GetCurrentSubscriptionUseCase,
    ExtendSubscriptionTrialPeriodUseCase,

    // Capability Use Cases
    CreateCapabilityUseCase,
    GetListOfCapabilitiesUseCase,

    // Membership Capability Use Cases
    GetMembershipCapabilitiesUseCase,

    // Utils
    BasicRetryUtil,
    DatabaseUtil,

    // Factories
    PaymentGatewayFactory,
    WebhookHandlerFactory,

    // External Services
    PaymentService,
    UserProfileService,

    // Payment Gateways
    StripePaymentGateway,
    PaypalPaymentGateway,

    // Handlers
    PaymentFailedHandler,
    InvoiceCreatedHandler,
    PaymentSucceededHandler,
    SubscriptionCancelledHandler,
  ],
})
// @Transporters([KafkaAdapter])
export class MembershipModule {}
