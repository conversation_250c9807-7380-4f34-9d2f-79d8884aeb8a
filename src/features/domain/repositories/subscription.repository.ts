import { Nullable } from "@heronjs/common";
import { ISubscription, ISubscriptionGatewayMapping } from "@features/domain/aggregates";
import { IRepository, QueryInputFindOne, RepositoryOptions } from "@cbidigital/aqua-ddd";

export interface ISubscriptionRepository extends IRepository<ISubscription, RepositoryOptions> {
  createGatewayMapping(
    entity: ISubscriptionGatewayMapping,
    options?: RepositoryOptions,
  ): Promise<ISubscriptionGatewayMapping>;
  findOneGatewayMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<ISubscriptionGatewayMapping>>;
}
