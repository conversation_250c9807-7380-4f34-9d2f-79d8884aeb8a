import { Nullable } from "@heronjs/common";
import { ICapability } from "@features/domain/aggregates";
import { QueryInput, QueryInputFindOne, RepositoryOptions } from "@cbidigital/aqua-ddd";

export interface ICapabilityRepository {
  create(entity: ICapability, options?: RepositoryOptions): Promise<ICapability>;

  update(entity: ICapability, options?: RepositoryOptions): Promise<ICapability>;

  findOne(input: QueryInputFindOne, options?: RepositoryOptions): Promise<Nullable<ICapability>>;

  find(input: QueryInput, options?: RepositoryOptions): Promise<ICapability[]>;

  count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number>;

  findByKey(key: string, options?: RepositoryOptions): Promise<Nullable<ICapability>>;
}
