import { Nullable } from "@heronjs/common";
import { QueryInput, RepositoryOptions, QueryInputFindOne } from "@cbidigital/aqua-ddd";
import { IMembershipCapability } from "@features/domain/aggregates/memberships/entities/membership-capability";

export interface IMembershipCapabilityRepository {
  create(
    entity: IMembershipCapability,
    options?: RepositoryOptions,
  ): Promise<IMembershipCapability>;

  update(
    entity: IMembershipCapability,
    options?: RepositoryOptions,
  ): Promise<IMembershipCapability>;

  findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipCapability>>;

  find(input: QueryInput, options?: RepositoryOptions): Promise<IMembershipCapability[]>;

  count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number>;

  findByMembershipId(
    membershipId: string,
    options?: RepositoryOptions,
  ): Promise<IMembershipCapability[]>;

  findByCapabilityId(
    capabilityId: string,
    options?: RepositoryOptions,
  ): Promise<IMembershipCapability[]>;

  findByMembershipAndCapability(
    membershipId: string,
    capabilityId: string,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipCapability>>;

  delete(id: string, options?: RepositoryOptions): Promise<void>;
}
