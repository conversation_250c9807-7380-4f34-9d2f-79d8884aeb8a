import {
  QueryInput,
  IRepository,
  RepositoryOptions,
  QueryInputFindOne,
} from "@cbidigital/aqua-ddd";
import {
  IMembership,
  IMembershipCycle,
  IMembershipGatewayMapping,
  IMembershipCycleGatewayMapping,
} from "@features/domain/aggregates";
import { Nullable } from "@heronjs/common";

export interface IMembershipRepository extends IRepository<IMembership, RepositoryOptions> {
  upsertList(entities: IMembership[], options: RepositoryOptions): Promise<IMembership[]>;

  count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number>;

  createGatewayMapping(
    entity: IMembershipGatewayMapping,
    options?: RepositoryOptions,
  ): Promise<IMembershipGatewayMapping>;

  findOneGatewayMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipGatewayMapping>>;

  findOneCycleGatewayMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipCycleGatewayMapping>>;

  createCycleGatewayMapping(
    entity: IMembershipCycleGatewayMapping,
    options?: RepositoryOptions,
  ): Promise<IMembershipCycleGatewayMapping>;

  findOneCycle(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipCycle>>;
}
