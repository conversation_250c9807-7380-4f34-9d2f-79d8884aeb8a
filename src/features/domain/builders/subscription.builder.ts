import {
  AggregateRootBuilder,
  IAggregateRootBuilder,
  AggregateRootBuilderPayload,
} from "@cbidigital/aqua-ddd";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Lifecycle, Provider } from "@heronjs/common";
import { ISubscription, Subscription } from "@features/domain/aggregates";

export type SubscriptionBuilderBuildPayload = AggregateRootBuilderPayload<ISubscription>;
export type ISubscriptionBuilder = IAggregateRootBuilder<ISubscription>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.BUILDER.SUBSCRIPTION,
  scope: Lifecycle.Singleton,
})
export class SubscriptionBuilder
  extends AggregateRootBuilder<ISubscription>
  implements ISubscriptionBuilder
{
  async build(payload?: SubscriptionBuilderBuildPayload): Promise<ISubscription> {
    return new Subscription(payload || {});
  }
}
