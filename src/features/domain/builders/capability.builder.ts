import {
  AggregateRootBuilder,
  IAggregateRootBuilder,
  AggregateRootBuilderPayload,
} from "@cbidigital/aqua-ddd";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Lifecycle, Provider } from "@heronjs/common";
import { ICapability, Capability } from "@features/domain/aggregates";

export type CapabilityBuilderBuildPayload = AggregateRootBuilderPayload<ICapability>;
export type ICapabilityBuilder = IAggregateRootBuilder<ICapability>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.BUILDER.CAPABILITY,
  scope: Lifecycle.Singleton,
})
export class CapabilityBuilder
  extends AggregateRootBuilder<ICapability>
  implements ICapabilityBuilder
{
  async build({
    id,
    props,
    externalProps,
  }: CapabilityBuilderBuildPayload = {}): Promise<ICapability> {
    return new Capability({ id, props, externalProps });
  }
}
