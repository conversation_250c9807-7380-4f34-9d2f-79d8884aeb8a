import {
  AggregateRootBuilder,
  IAggregateRootBuilder,
  AggregateRootBuilderPayload,
} from "@cbidigital/aqua-ddd";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Lifecycle, Provider } from "@heronjs/common";
import { ICapability, Capability } from "@features/domain/aggregates";

export type WebhookEventBuilderBuildPayload = AggregateRootBuilderPayload<ICapability>;
export type IWebhookEventBuilder = IAggregateRootBuilder<ICapability>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.BUILDER.WEBHOOK_EVENT,
  scope: Lifecycle.Singleton,
})
export class WebhookeEventBuilder
  extends AggregateRootBuilder<ICapability>
  implements IWebhookEventBuilder
{
  async build({
    id,
    props,
    externalProps,
  }: WebhookEventBuilderBuildPayload = {}): Promise<ICapability> {
    return new Capability({ id, props, externalProps });
  }
}
