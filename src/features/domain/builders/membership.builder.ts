import {
  AggregateRootBuilder,
  IAggregateRootBuilder,
  AggregateRootBuilderPayload,
} from "@cbidigital/aqua-ddd";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Lifecycle, Provider } from "@heronjs/common";
import { IMembership, Membership } from "@features/domain/aggregates";

export type MembershipBuilderBuildPayload = AggregateRootBuilderPayload<IMembership>;
export type IMembershipBuilder = IAggregateRootBuilder<IMembership>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.BUILDER.MEMBERSHIP,
  scope: Lifecycle.Singleton,
})
export class MembershipBuilder
  extends AggregateRootBuilder<IMembership>
  implements IMembershipBuilder
{
  async build({
    id,
    props,
    externalProps,
  }: MembershipBuilderBuildPayload = {}): Promise<IMembership> {
    return new Membership({ id, props, externalProps });
  }
}
