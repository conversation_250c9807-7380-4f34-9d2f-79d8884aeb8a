import {
  AggregateRoot,
  IAggregateRoot,
  AggregateRootConstructorPayload,
} from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";

export type CreateCapabilityInput = {
  key: string;
  description: string;
};

export type UpdateCapabilityInput = {
  id: string;
  key?: string;
  description?: string;
};

export type CapabilityProps = {
  key: string;
  description: string;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type CapabilityMethods = {
  create(input: CreateCapabilityInput): Promise<void>;
  update(input: UpdateCapabilityInput): Promise<void>;
};

export type ICapability = IAggregateRoot<CapabilityProps, CapabilityMethods>;

export class Capability
  extends AggregateRoot<CapabilityProps, CapabilityMethods>
  implements ICapability
{
  static readonly AGGREGATE_NAME = "capability";

  constructor(props: AggregateRootConstructorPayload<CapabilityProps>) {
    super(props);
  }

  /** Props **/
  get key(): string {
    return this.props.key;
  }

  get description(): string {
    return this.props.description;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/
  private setKey(payload?: string) {
    if (payload !== undefined) this.setProp("key", payload);
  }

  private setDescription(payload?: string) {
    if (payload !== undefined) this.setProp("description", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Nullable<Date>) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async create(payload: CreateCapabilityInput) {
    this.setId(randomUUID());
    this.setKey(payload.key);
    this.setDescription(payload.description);
    this.setCreatedAt(new Date());
  }

  async update(payload: UpdateCapabilityInput) {
    this.setId(payload.id);
    this.setKey(payload.key);
    this.setDescription(payload.description);
    this.setUpdatedAt(new Date());
  }
}
