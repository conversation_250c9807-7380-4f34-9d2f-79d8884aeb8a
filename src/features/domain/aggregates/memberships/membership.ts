import {
  AggregateRoot,
  IAggregateRoot,
  AggregateRootConstructorPayload,
} from "@cbidigital/aqua-ddd";
import {
  MembershipCycle,
  IMembershipCycle,
  MembershipStatusEnum,
  CreateMembershipInput,
  UpdateMembershipInput,
  IMembershipCapability,
  UpdateMembershipCycleInput,
  DuplicateBillingCycleError,
  CreateMembershipCycleInput,
  CapabilityAlreadyExistsError,
  CreateMembershipCapabilityInput,
  UpdateMembershipCapabilityInput,
  MissingTenantIdForCustomMembershipError,
  CannotChangeCustomToNonCustomError,
} from "@features/domain/aggregates";
import { randomUUID } from "crypto";
import { Nullable, Optional } from "@heronjs/common";
import { MembershipCapability } from "@features/domain/aggregates/memberships/entities";

export type MembershipProps = {
  name: string;
  isCustom: boolean;
  isTrial: boolean;
  tenantId: Nullable<string>;
  status: MembershipStatusEnum;
  createdAt: Date;
  createdBy: string;
  description: string;
  updatedBy: Nullable<string>;
  updatedAt: Nullable<Date>;
  cycles: Nullable<IMembershipCycle[]>;
  upsertedCycles?: IMembershipCycle[];
  deletedCycles?: IMembershipCycle[];
  capabilities: Nullable<IMembershipCapability[]>;
  upsertedCapabilities?: IMembershipCapability[];
  deletedCapabilities?: IMembershipCapability[];
};

export type MembershipMethods = {
  delete(): Promise<void>;
  create(payload: CreateMembershipInput, context: { auth: { authId: string } }): Promise<void>;
  update(payload: UpdateMembershipInput, context: { auth: { authId: string } }): Promise<void>;
  addMembershipCycle(payload: CreateMembershipCycleInput): Promise<void>;
  updateMembershipCycle(payload: UpdateMembershipCycleInput): Promise<void>;
  removeMembershipCycle(cycleId: string): Promise<void>;
  addCapability(payload: CreateMembershipCapabilityInput): Promise<void>;
  updateCapability(payload: UpdateMembershipCapabilityInput): Promise<void>;
  removeCapability(capabilityId: string): Promise<void>;
};
export type IMembership = IAggregateRoot<MembershipProps, MembershipMethods>;
export class Membership
  extends AggregateRoot<MembershipProps, MembershipMethods>
  implements IMembership
{
  static AGGREGATE_NAME = "membership";

  constructor(payload: AggregateRootConstructorPayload<MembershipProps>) {
    super(payload);
  }

  /** Props **/

  get tenantId(): Nullable<string> {
    return this.props.tenantId;
  }

  get name(): string {
    return this.props.name;
  }

  get description(): string {
    return this.props.description;
  }

  get isCustom(): boolean {
    return this.props.isCustom;
  }

  get isTrial(): boolean {
    return this.props.isTrial;
  }

  get status(): MembershipStatusEnum {
    return this.props.status;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get createdBy(): string {
    return this.props.createdBy;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  get updatedBy(): Nullable<string> {
    return this.props.updatedBy;
  }

  get cycles(): Nullable<IMembershipCycle[]> {
    return this.props.cycles;
  }

  get upsertedCycles(): Optional<IMembershipCycle[]> {
    return this.props.upsertedCycles;
  }

  get capabilities(): Nullable<IMembershipCapability[]> {
    return this.props.capabilities;
  }

  get upsertedCapabilities(): Optional<IMembershipCapability[]> {
    return this.props.upsertedCapabilities;
  }

  get deletedCycles(): Optional<IMembershipCycle[]> {
    return this.props.deletedCycles;
  }

  get deletedCapabilities(): Optional<IMembershipCapability[]> {
    return this.props.deletedCapabilities;
  }

  /** Methods **/

  private setTenantId(payload?: Nullable<string>) {
    if (payload !== undefined) this.setProp("tenantId", payload);
  }

  private setName(payload?: string) {
    if (payload !== undefined) this.setProp("name", payload);
  }

  private setDescription(payload?: string) {
    if (payload !== undefined) this.setProp("description", payload);
  }

  private setIsCustom(payload?: boolean) {
    if (payload !== undefined) this.setProp("isCustom", payload);
  }

  private setIsTrial(payload?: boolean) {
    if (payload !== undefined) this.setProp("isTrial", payload);
  }

  private setStatus(payload?: MembershipStatusEnum) {
    if (payload !== undefined) this.setProp("status", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setCreatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("createdBy", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  private setUpdatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("updatedBy", payload);
  }

  private setCycles(payload?: IMembershipCycle[]) {
    if (payload !== undefined) this.setProp("cycles", payload);
  }

  private setUpsertedCycles(payload?: IMembershipCycle[]) {
    if (payload !== undefined) this.setProp("upsertedCycles", payload);
  }

  private setDeletedCycles(payload?: IMembershipCycle[]) {
    if (payload !== undefined) this.setProp("deletedCycles", payload);
  }

  private setCapabilities(payload?: IMembershipCapability[]) {
    if (payload !== undefined) this.setProp("capabilities", payload);
  }

  private setUpsertedCapabilities(payload?: IMembershipCapability[]) {
    if (payload !== undefined) this.setProp("upsertedCapabilities", payload);
  }

  private setDeletedCapabilities(payload?: IMembershipCapability[]) {
    if (payload !== undefined) this.setProp("deletedCapabilities", payload);
  }

  async create(payload: CreateMembershipInput, context: { auth: { authId: string } }) {
    if (payload.isCustom && !payload.tenantId) {
      throw new MissingTenantIdForCustomMembershipError();
    }
    this.setId(randomUUID());
    this.setName(payload.name);
    this.setIsTrial(payload.isTrial);
    this.setTenantId(payload.tenantId);
    this.setIsCustom(payload.isCustom);
    this.setDescription(payload.description);
    this.setStatus(MembershipStatusEnum.ACTIVE);
    this.setCreatedAt(new Date());
    this.setCreatedBy(context.auth.authId);
    this.setUpsertedCycles([]);
    this.setUpsertedCapabilities([]);

    // Add cycles if provided
    if (payload.cycles?.length) {
      const promises = payload.cycles.map(async (cycle) => {
        await this.addMembershipCycle({
          ...cycle,
          membershipId: this.id,
          createdBy: context.auth.authId,
        });
      });

      await Promise.all(promises);
    }

    // Add capabilities if provided
    if (payload.capabilities?.length) {
      const promises = payload.capabilities.map(async (capability) => {
        await this.addCapability({
          ...capability,
          membershipId: this.id,
        });
      });
      await Promise.all(promises);
    }
  }

  private detectChangeType<T extends { id?: string }>(entity: T) {
    const keys = Object.keys(entity);
    let changeType: "add" | "update" | "remove" | null = null;
    if (keys.length === 1 && "id" in entity) changeType = "remove";
    if (keys.length > 1 && "id" in entity) changeType = "update";
    if (keys.length > 1 && !("id" in entity)) changeType = "add";
    return changeType;
  }

  async update(
    payload: UpdateMembershipInput,
    context: { auth: { authId: string } },
  ): Promise<void> {
    if (this.isCustom && payload.isCustom === false) throw new CannotChangeCustomToNonCustomError();
    this.setId(payload.id);
    this.setName(payload.name);
    this.setIsTrial(payload.isTrial);
    this.setIsCustom(payload.isCustom);
    this.setTenantId(payload.tenantId);
    this.setDescription(payload.description);
    this.setUpdatedAt(new Date());
    this.setUpdatedBy(context.auth.authId);

    // Clear upserted and deleted arrays
    this.setUpsertedCycles([]);
    this.setUpsertedCapabilities([]);
    this.setDeletedCycles([]);
    this.setDeletedCapabilities([]);

    if (payload?.cycles) {
      for (const c of payload.cycles) {
        const changeType = this.detectChangeType(c);
        switch (changeType) {
          case "add":
            await this.addMembershipCycle(c as CreateMembershipCycleInput);
            break;
          case "update":
            await this.updateMembershipCycle(c as UpdateMembershipCycleInput);
            break;
          case "remove":
            await this.removeMembershipCycle(c.id!);
            break;
        }
      }
    }

    if (payload?.capabilities) {
      for (const cap of payload.capabilities) {
        const changeType = this.detectChangeType(cap);
        switch (changeType) {
          case "add":
            await this.addCapability(cap as CreateMembershipCapabilityInput);
            break;
          case "update":
            await this.updateCapability(cap as UpdateMembershipCapabilityInput);
            break;
          case "remove":
            await this.removeCapability(cap.id!);
            break;
        }
      }
    }

    // this.addDomainEvent(PURCHASE_ORDER_EVENT_NAMES.UPDATE, eventMetadata);
  }

  /**
   * Adds a membership cycle to this membership
   *
   * @param payload - The membership cycle to add
   * @throws Error if validation fails (missing tenantId for custom memberships or duplicate billing cycle)
   */
  async addMembershipCycle(payload: CreateMembershipCycleInput): Promise<void> {
    // Validate based on membership type
    if (this.isCustom && !this.tenantId) {
      // Custom memberships require a tenantId
      throw new MissingTenantIdForCustomMembershipError();
    }
    //  memberships can't have duplicate billing cycles
    const hasDuplicateCycle = this.props.cycles?.some(
      (item) => item.billingCycle === payload.billingCycle,
    );
    if (hasDuplicateCycle) throw new DuplicateBillingCycleError(payload.billingCycle);

    const membershipCycle = new MembershipCycle();
    await membershipCycle.create({
      ...payload,
      membershipId: this.id,
      createdBy: this.updatedBy ?? this.createdBy,
    });
    this.upsertedCycles?.push(membershipCycle);
  }

  async updateMembershipCycle(payload: UpdateMembershipCycleInput) {
    if (!this.cycles) return;
    const cycle = this.cycles.find((cycle) => cycle.id === payload.id);
    if (!cycle) return;
    await cycle.update({
      ...payload,
      updatedBy: this.updatedBy ?? this.createdBy,
    });
    this.upsertedCycles?.push(cycle);
  }

  async removeMembershipCycle(cycleId: string): Promise<void> {
    // Find the cycle to remove
    const cycleToRemove = this.cycles?.find((cycle) => cycle.id === cycleId);
    if (!cycleToRemove) return;

    // Add to deleted cycles
    this.deletedCycles?.push(cycleToRemove);

    // Remove from cycles
    this.setCycles(this.cycles?.filter((cycle) => cycle.id !== cycleId));
  }

  /**
   * Adds a capability to this membership
   *
   * @param payload - The capability to add
   * @returns The ID of the newly added capability
   */
  async addCapability(payload: CreateMembershipCapabilityInput) {
    // Check if capability already exists
    const existingCapability = this.capabilities?.find(
      (cap) => cap.capabilityId === payload.capabilityId,
    );
    if (existingCapability) throw new CapabilityAlreadyExistsError();

    // Create new capability
    const newCapability = new MembershipCapability();
    await newCapability.create({
      ...payload,
      membershipId: this.id,
    });

    this.upsertedCapabilities?.push(newCapability);
  }

  /**
   * Updates a capability in this membership
   *
   * @param payload - The capability to update
   */
  async updateCapability(payload: UpdateMembershipCapabilityInput) {
    if (!this.capabilities?.length) return;
    const existingCapability = this.capabilities.find((cap) => cap.id === payload.id);
    if (!existingCapability) return;
    await existingCapability.update(payload);
    this.upsertedCapabilities?.push(existingCapability);
  }

  /**
   * Removes a capability from this membership
   *
   * @param capabilityId - The ID of the capability to remove
   */
  async removeCapability(capabilityId: string): Promise<void> {
    // Find the capability to remove
    const capabilityToRemove = this.capabilities?.find((cap) => cap.capabilityId === capabilityId);

    if (!capabilityToRemove) return;

    // Add to deleted capabilities
    this.deletedCapabilities?.push(capabilityToRemove);

    // Remove from capabilities
    this.setCapabilities(this.capabilities?.filter((cap) => cap.id !== capabilityId));
  }

  async delete(): Promise<void> {
    // this.addDomainEvent(PURCHASE_ORDER_EVENT_NAMES.DELETE, eventMetadata);
  }
}
