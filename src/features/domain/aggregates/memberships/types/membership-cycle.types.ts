import { MembershipCycleEnum } from "@features/domain/aggregates/memberships/enums";

export type CreateMembershipCycleInput = {
  membershipId: string;
  billingCycle: MembershipCycleEnum;
  amount: number;
  createdBy: string;
};

export type UpdateMembershipCycleInput = {
  id: string;
  updatedBy?: string;
} & Partial<CreateMembershipCycleInput>;

export type UpsertMembershipCycleInput = Partial<UpdateMembershipCycleInput>;
