import {
  CreateMembershipCycleInput,
  UpsertMembershipCycleInput,
  CreateMembershipCapabilityInput,
  UpsertMembershipCapabilityInput,
} from "@features/domain/aggregates/memberships";
import { Nullable } from "@heronjs/common";

export type CreateMembershipInput = {
  tenantId: Nullable<string>;
  name: string;
  isCustom: boolean;
  isTrial: boolean;
  description?: string;
  cycles?: Omit<CreateMembershipCycleInput, "membershipId" | "createdBy">[];
  capabilities?: Omit<CreateMembershipCapabilityInput, "membershipId">[];
};

export type UpdateMembershipInput = Partial<
  Omit<CreateMembershipInput, "cycles" | "capabilities">
> & {
  id: string;
  cycles?: UpsertMembershipCycleInput[];
  capabilities?: UpsertMembershipCapabilityInput[];
};
