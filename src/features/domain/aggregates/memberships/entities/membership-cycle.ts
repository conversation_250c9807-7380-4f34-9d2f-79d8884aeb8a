import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";
import { Entity, IEntity, EntityConstructorPayload } from "@cbidigital/aqua-ddd";
import {
  MembershipCycleEnum,
  CreateMembershipCycleInput,
  UpdateMembershipCycleInput,
  IMembershipCycleGatewayMapping,
} from "@features/domain";

export type MembershipCycleProps = {
  membershipId: string;
  billingCycle: MembershipCycleEnum;
  amount: number;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
  gatewayMappings: Nullable<IMembershipCycleGatewayMapping[]>;
};

export type MembershipCycleMethods = {
  create(input: CreateMembershipCycleInput): Promise<void>;
  update(input: UpdateMembershipCycleInput): Promise<void>;
  getGatewayMapping(provider?: string): Nullable<IMembershipCycleGatewayMapping>;
};

export type IMembershipCycle = IEntity<MembershipCycleProps, MembershipCycleMethods>;

export class MembershipCycle
  extends Entity<MembershipCycleProps, MembershipCycleMethods>
  implements IMembershipCycle
{
  constructor(props?: EntityConstructorPayload<MembershipCycleProps>) {
    super(props);
  }

  /** Props **/
  get membershipId(): string {
    return this.props.membershipId;
  }

  get billingCycle(): MembershipCycleEnum {
    return this.props.billingCycle;
  }

  get amount(): number {
    return this.props.amount;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get createdBy(): string {
    return this.props.createdBy;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  get updatedBy(): Nullable<string> {
    return this.props.updatedBy;
  }

  get gatewayMappings(): Nullable<IMembershipCycleGatewayMapping[]> {
    return this.props.gatewayMappings;
  }

  /** Methods **/
  private setMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipId", payload);
  }

  private setBillingCycle(payload?: MembershipCycleEnum) {
    if (payload !== undefined) this.setProp("billingCycle", payload);
  }

  private setAmount(payload?: number) {
    if (payload !== undefined) this.setProp("amount", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setCreatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("createdBy", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  private setUpdatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("updatedBy", payload);
  }

  private setGatewayMappings(payload?: IMembershipCycleGatewayMapping[]) {
    if (payload !== undefined) this.setProp("gatewayMappings", payload);
  }

  async create(payload: CreateMembershipCycleInput) {
    // handle logic
    this.setId(randomUUID());
    this.setMembershipId(payload.membershipId);
    this.setBillingCycle(payload.billingCycle);
    this.setAmount(payload.amount);
    this.setCreatedAt(new Date());
    this.setCreatedBy(payload.createdBy);
  }

  async update(payload: UpdateMembershipCycleInput) {
    // handle logic
    this.setId(payload.id);
    this.setMembershipId(payload.membershipId);
    this.setBillingCycle(payload.billingCycle);
    this.setAmount(payload.amount);
    this.setUpdatedAt(new Date());
    this.setUpdatedBy(payload.updatedBy);
  }

  getGatewayMapping(provider: string): Nullable<IMembershipCycleGatewayMapping> {
    if (!this.gatewayMappings) return null;
    return this.gatewayMappings.find((m) => m.gateway === provider) || null;
  }
}
