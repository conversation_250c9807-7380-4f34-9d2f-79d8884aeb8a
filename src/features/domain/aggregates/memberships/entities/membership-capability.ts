import { Entity, IEntity, EntityConstructorPayload } from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";
import {
  CreateMembershipCapabilityInput,
  UpdateMembershipCapabilityInput,
} from "@features/domain/aggregates/memberships/types";

export type MembershipCapabilityProps = {
  membershipId: string;
  capabilityId: string;
  value: string;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type MembershipCapabilityMethods = {
  create(input: CreateMembershipCapabilityInput): Promise<void>;
  update(input: UpdateMembershipCapabilityInput): Promise<void>;
};

export type IMembershipCapability = IEntity<MembershipCapabilityProps, MembershipCapabilityMethods>;

export class MembershipCapability
  extends Entity<MembershipCapabilityProps, MembershipCapabilityMethods>
  implements IMembershipCapability
{
  static readonly ENTITY_NAME = "MembershipCapability";

  constructor(props?: EntityConstructorPayload<MembershipCapabilityProps>) {
    super(props);
  }

  /** Props **/
  get membershipId(): string {
    return this.props.membershipId;
  }

  get capabilityId(): string {
    return this.props.capabilityId;
  }

  get value(): string {
    return this.props.value;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/
  private setMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipId", payload);
  }

  private setCapabilityId(payload?: string) {
    if (payload !== undefined) this.setProp("capabilityId", payload);
  }

  private setValue(payload?: string) {
    if (payload !== undefined) this.setProp("value", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Nullable<Date>) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async create(payload: CreateMembershipCapabilityInput) {
    this.setId(randomUUID());
    this.setMembershipId(payload.membershipId);
    this.setCapabilityId(payload.capabilityId);
    this.setValue(payload.value);
    this.setCreatedAt(new Date());
  }

  async update(payload: UpdateMembershipCapabilityInput) {
    this.setId(payload.id);
    this.setValue(payload.value);
    this.setUpdatedAt(new Date());
  }
}
