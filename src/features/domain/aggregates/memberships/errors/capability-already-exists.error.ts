import { RuntimeError } from "@heronjs/common";
import { Membership } from "@features/domain/aggregates/memberships/membership";
import { MembershipErrorCodes } from "@features/domain/aggregates/memberships/errors/errors";

export class CapabilityAlreadyExistsError extends RuntimeError {
  constructor() {
    super(
      Membership.AGGREGATE_NAME,
      MembershipErrorCodes.CAPABILITY_ALREADY_EXISTS,
      "Capability already exists in this membership.",
    );
  }
}
