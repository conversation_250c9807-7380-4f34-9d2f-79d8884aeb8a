import { RuntimeError } from "@heronjs/common";
import { Membership } from "@features/domain/aggregates/memberships/membership";
import { MembershipErrorCodes } from "@features/domain/aggregates/memberships/errors/errors";

export class MissingTenantIdForCustomMembershipError extends RuntimeError {
  constructor() {
    super(
      Membership.AGGREGATE_NAME,
      MembershipErrorCodes.MISSING_TENANT_ID,
      "Tenant ID is required for custom memberships.",
    );
  }
}
