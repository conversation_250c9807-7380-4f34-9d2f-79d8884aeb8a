import { RuntimeError } from "@heronjs/common";
import { Membership } from "@features/domain/aggregates/memberships/membership";
import { MembershipErrorCodes } from "@features/domain/aggregates/memberships/errors/errors";

export class DuplicateBillingCycleError extends RuntimeError {
  constructor(billingCycle: string) {
    super(
      Membership.AGGREGATE_NAME,
      MembershipErrorCodes.DUPLICATE_BILLING_CYCLE,
      `Duplicate billing cycle: '${billingCycle}' already exists in this membership.`,
    );
  }
}
