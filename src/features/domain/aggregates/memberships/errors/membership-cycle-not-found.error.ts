import { RuntimeError } from "@heronjs/common";
import { Membership } from "@features/domain/aggregates/memberships/membership";
import { MembershipErrorCodes } from "@features/domain/aggregates/memberships/errors/errors";

export class MembershipCycleNotFoundError extends RuntimeError {
  constructor() {
    super(Membership.AGGREGATE_NAME, MembershipErrorCodes.NOT_FOUND, "Membership cycle not found.");
  }
}
