import { RuntimeError } from "@heronjs/common";
import { Membership } from "@features/domain/aggregates/memberships/membership";
import { MembershipErrorCodes } from "@features/domain/aggregates/memberships/errors/errors";

export class CannotChangeCustomToNonCustomError extends RuntimeError {
  constructor() {
    super(
      Membership.AGGREGATE_NAME,
      MembershipErrorCodes.CANNOT_CHANGE_CUSTOM_TO_NON_CUSTOM,
      "Cannot change a custom membership to a non-custom membership",
    );
  }
}
