import { PaymentGatewayProvider } from "@features/domain/services";

export type CreateSubscriptionInput = {
  tenantId: string;
  membershipId: string;
  membershipCycleId: string;
  paymentMethodId?: string;
  gateway?: PaymentGatewayProvider;
  isTrial?: boolean;
  trialPeriodDays?: number;
  createdBy: string;
};

export type UpdateSubscriptionInput = {
  id: string;
  isActive?: boolean;
  updatedBy?: string;
} & Partial<Omit<CreateSubscriptionInput, "createdBy">>;
