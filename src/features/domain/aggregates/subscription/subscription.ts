import { randomUUID } from "crypto";
import { Nullable, Optional } from "@heronjs/common";
import { PaymentGatewayProvider } from "@features/domain/services";
import { CreateSubscriptionInput, UpdateSubscriptionInput } from "./types";
import { Invoice, IInvoice, CreateInvoiceInput, ISubscriptionGatewayMapping } from "./entities";
import {
  AggregateRoot,
  IAggregateRoot,
  AggregateRootConstructorPayload,
} from "@cbidigital/aqua-ddd";
import {
  InvoiceStatusEnum,
  SubscriptionStatusEnum,
} from "@features/domain/aggregates/subscription/enums";

export type SubscriptionProps = {
  tenantId: string;
  membershipId: string;
  membershipCycleId: string;
  paymentMethodId: Nullable<string>;
  status: SubscriptionStatusEnum;
  trialEnd: Nullable<number>;
  gateway: Nullable<PaymentGatewayProvider>;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
  invoices: IInvoice[];
  subscriptionGatewayMappings?: ISubscriptionGatewayMapping[];
};

export type SubscriptionMethods = {
  create(payload: CreateSubscriptionInput): Promise<void>;
  update(payload: UpdateSubscriptionInput): Promise<void>;
  cancel(): Promise<void>;
  expire(): Promise<void>;
  activate(): Promise<void>;
  deactivate(): Promise<void>;
  addInvoice(payload: Omit<CreateInvoiceInput, "subscriptionId">): Promise<IInvoice>;
  getInvoices(): IInvoice[];
  getPendingInvoice(): Nullable<IInvoice>;
  extendTrialPeriod(days: number): Promise<void>;
  onInvoiceCreated(): Promise<void>;
  onPaymentSucceeded(): Promise<void>;
  onPaymentFailed(): Promise<void>;
  onPaymentCancelled(): Promise<void>;
  getSubscriptionGatewayMapping(gateway: string): Nullable<ISubscriptionGatewayMapping>;
};

export type ISubscription = IAggregateRoot<SubscriptionProps, SubscriptionMethods>;

export class Subscription
  extends AggregateRoot<SubscriptionProps, SubscriptionMethods>
  implements ISubscription
{
  static AGGREGATE_NAME = "subscription";

  constructor(payload: AggregateRootConstructorPayload<SubscriptionProps>) {
    super(payload);
  }

  /** Props **/
  get tenantId(): string {
    return this.props.tenantId;
  }

  get membershipId(): string {
    return this.props.membershipId;
  }

  get membershipCycleId(): string {
    return this.props.membershipCycleId;
  }

  get paymentMethodId(): Nullable<string> {
    return this.props.paymentMethodId;
  }

  get status(): SubscriptionStatusEnum {
    return this.props.status;
  }

  get trialEnd(): Nullable<number> {
    return this.props.trialEnd;
  }

  get gateway(): Nullable<PaymentGatewayProvider> {
    return this.props.gateway;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get createdBy(): string {
    return this.props.createdBy;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  get updatedBy(): Nullable<string> {
    return this.props.updatedBy;
  }

  get invoices(): IInvoice[] {
    return this.props.invoices;
  }

  get subscriptionGatewayMappings(): Optional<ISubscriptionGatewayMapping[]> {
    return this.props.subscriptionGatewayMappings;
  }

  /** Methods **/
  private setTenantId(payload?: string) {
    if (payload !== undefined) this.setProp("tenantId", payload);
  }

  private setMembershipId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipId", payload);
  }

  private setMembershipCycleId(payload?: string) {
    if (payload !== undefined) this.setProp("membershipCycleId", payload);
  }

  private setPaymentMethodId(payload?: string) {
    if (payload !== undefined) this.setProp("paymentMethodId", payload);
  }

  private setStatus(payload?: SubscriptionStatusEnum) {
    if (payload !== undefined) this.setProp("status", payload);
  }

  private setTrialEnd(payload?: number) {
    if (payload !== undefined) this.setProp("trialEnd", payload);
  }

  private setGateway(payload?: Nullable<PaymentGatewayProvider>) {
    if (payload !== undefined) this.setProp("gateway", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setCreatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("createdBy", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  private setUpdatedBy(payload?: string) {
    if (payload !== undefined) this.setProp("updatedBy", payload);
  }

  async create(payload: CreateSubscriptionInput): Promise<void> {
    this.setId(randomUUID());
    this.setTenantId(payload.tenantId);
    this.setMembershipId(payload.membershipId);
    this.setMembershipCycleId(payload.membershipCycleId);
    this.setPaymentMethodId(payload.paymentMethodId);
    this.setGateway(payload.gateway);
    this.setCreatedAt(new Date());
    this.setCreatedBy(payload.createdBy);
    this.setProp("invoices", []);
    const status = payload.isTrial
      ? SubscriptionStatusEnum.TRIALING
      : SubscriptionStatusEnum.PENDING;
    this.setStatus(status);
    if (payload.isTrial && payload.trialPeriodDays) {
      this.setTrialEnd(Date.now() + payload.trialPeriodDays * 24 * 60 * 60 * 1000);
    }
  }

  async update(payload: UpdateSubscriptionInput): Promise<void> {
    this.setId(payload.id);
    this.setTenantId(payload.tenantId);
    this.setMembershipCycleId(payload.membershipCycleId);
    this.setUpdatedAt(new Date());
    this.setUpdatedBy(payload.updatedBy);
  }

  async cancel(): Promise<void> {
    this.setStatus(SubscriptionStatusEnum.CANCELED);
    this.setUpdatedAt(new Date());
  }

  async expire(): Promise<void> {
    this.setUpdatedAt(new Date());
  }

  async activate(): Promise<void> {
    this.setStatus(SubscriptionStatusEnum.ACTIVE);
    this.setUpdatedAt(new Date());
  }

  async deactivate(): Promise<void> {
    this.setUpdatedAt(new Date());
  }

  async onPaymentFailed(): Promise<void> {
    if (this.status === SubscriptionStatusEnum.ACTIVE) {
      this.setStatus(SubscriptionStatusEnum.EXPIRED);
    }
    this.setUpdatedAt(new Date());
  }

  async onPaymentCancelled(): Promise<void> {
    this.setUpdatedAt(new Date());
  }

  async onInvoiceCreated(): Promise<void> {
    this.setUpdatedAt(new Date());
  }

  async onPaymentSucceeded(): Promise<void> {
    this.setStatus(SubscriptionStatusEnum.ACTIVE);
    this.setUpdatedAt(new Date());
  }

  /**
   * Adds a membership change with the provided details
   *
   * @param payload - The membership change input data
   * @returns The created membership change entity
   */
  async addInvoice(payload: Omit<CreateInvoiceInput, "subscriptionId">): Promise<IInvoice> {
    const invoice = new Invoice();
    await invoice.create({ ...payload, subscriptionId: this.id });

    const invoices = [...this.invoices, invoice];
    this.setProp("invoices", invoices);

    return invoice;
  }

  getInvoices(): IInvoice[] {
    return this.invoices;
  }

  getPendingInvoice(): Nullable<IInvoice> {
    if (!this.invoices.length) return null;
    const currInvoice = this.invoices.find(
      (invoice) => invoice.status === InvoiceStatusEnum.PENDING,
    );

    return currInvoice ?? null;
  }

  getSubscriptionGatewayMapping(provider: string): Nullable<ISubscriptionGatewayMapping> {
    if (!this.subscriptionGatewayMappings?.length) return null;
    return this.subscriptionGatewayMappings.find((m) => m.gateway === provider) || null;
  }

  async extendTrialPeriod(days: number): Promise<void> {
    const DAY_IN_MS = 24 * 60 * 60 * 1000;
    const now = Date.now();
    this.setTrialEnd(now + days * DAY_IN_MS);
  }
}
