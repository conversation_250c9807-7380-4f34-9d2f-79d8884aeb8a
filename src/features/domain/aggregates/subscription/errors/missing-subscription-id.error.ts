import { RuntimeError } from "@heronjs/common";
import { Subscription } from "@features/domain/aggregates/subscription/subscription";
import { SubscriptionErrorCodes } from "@features/domain/aggregates/subscription/errors/errors";

export class MissingSubscriptionIdError extends RuntimeError {
  constructor() {
    super(
      Subscription.AGGREGATE_NAME,
      SubscriptionErrorCodes.MISSING_SUBSCRIPTION_ID,
      "Subscription id is required.",
    );
  }
}
