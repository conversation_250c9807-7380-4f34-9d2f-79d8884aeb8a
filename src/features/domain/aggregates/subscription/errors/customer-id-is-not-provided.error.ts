import { RuntimeError } from "@heronjs/common";
import { Subscription } from "@features/domain/aggregates/subscription/subscription";
import { SubscriptionErrorCodes } from "@features/domain/aggregates/subscription/errors/errors";

export class CustomerIdIsNotProvidedError extends RuntimeError {
  constructor() {
    super(
      Subscription.AGGREGATE_NAME,
      SubscriptionErrorCodes.CUSTOMER_ID_IS_NOT_PROVIDED,
      "Customer id is not provided.",
    );
  }
}
