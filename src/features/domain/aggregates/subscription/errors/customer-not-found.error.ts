import { RuntimeError } from "@heronjs/common";
import { Subscription } from "@features/domain/aggregates/subscription/subscription";
import { SubscriptionErrorCodes } from "@features/domain/aggregates/subscription/errors/errors";

export class CustomerNotFoundError extends RuntimeError {
  constructor() {
    super(
      Subscription.AGGREGATE_NAME,
      SubscriptionErrorCodes.CUSTOMER_NOT_FOUND,
      "Customer not found.",
    );
  }
}
