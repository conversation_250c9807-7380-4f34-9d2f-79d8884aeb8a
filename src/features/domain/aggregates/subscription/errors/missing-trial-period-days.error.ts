import { RuntimeError } from "@heronjs/common";
import { Subscription } from "@features/domain/aggregates/subscription/subscription";
import { SubscriptionErrorCodes } from "@features/domain/aggregates/subscription/errors/errors";

export class MissingTrialPeriodDaysError extends RuntimeError {
  constructor() {
    super(
      Subscription.AGGREGATE_NAME,
      SubscriptionErrorCodes.MISSING_TRIAL_PERIOD_DAYS,
      "Trial period days is required for trial subscriptions.",
    );
  }
}
