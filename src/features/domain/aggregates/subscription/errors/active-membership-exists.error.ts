import { RuntimeError } from "@heronjs/common";
import { Subscription } from "@features/domain/aggregates/subscription/subscription";
import { SubscriptionErrorCodes } from "@features/domain/aggregates/subscription/errors/errors";

export class ActiveMembershipExistsError extends RuntimeError {
  constructor() {
    super(
      Subscription.AGGREGATE_NAME,
      SubscriptionErrorCodes.ACTIVE_MEMBERSHIP_EXISTS,
      "Please cancel the current membership before assigning a new one.",
    );
  }
}
