import { RuntimeError } from "@heronjs/common";
import { Subscription } from "@features/domain/aggregates/subscription/subscription";
import { SubscriptionErrorCodes } from "@features/domain/aggregates/subscription/errors/errors";

export class RetrieveInvoiceError extends RuntimeError {
  constructor() {
    super(
      Subscription.AGGREGATE_NAME,
      SubscriptionErrorCodes.RETRIEVE_INVOICE_FAILED,
      "Failed to retrieve invoice.",
    );
  }
}
