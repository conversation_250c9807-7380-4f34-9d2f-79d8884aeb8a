export enum SubscriptionErrorNamespaces {
  SUBSCRIPTION = "subscription",
}

export enum SubscriptionErrorCodes {
  NOT_FOUND = 10000, // Tenant membership not found
  IS_THE_SAME_MEMBERSHIP = 10001, // Tenant membership is the same as the current one
  NOT_TRIAL_MEMBERSHIP = 10002, // Tenant membership is not a trial membership
  NOT_ACTIVE_BILLING_CYCLE = 10003, // Tenant membership does not have an active billing cycle
  USER_NOT_FOUND = 10004,
  SUBSCRIPTION_NOT_FOUND = 10005,
  RETRIEVE_INVOICE_FAILED = 10006,
  MISSING_TRIAL_PERIOD_DAYS = 10007,
  MISSING_SUBSCRIPTION_ID = 10008,
  CUSTOMER_ID_IS_NOT_PROVIDED = 10009,
  CUSTOMER_NOT_FOUND = 10010,
  ACTIVE_MEMBERSHIP_EXISTS = 10011, // Active membership already exists for tenant
  PENDING_BILLING_CYCLE_NOT_FOUND = 10012,
}
