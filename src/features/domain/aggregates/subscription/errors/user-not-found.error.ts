import { RuntimeError } from "@heronjs/common";
import { Subscription } from "@features/domain/aggregates/subscription/subscription";
import { SubscriptionErrorCodes } from "@features/domain/aggregates/subscription/errors/errors";

export class UserNotFoundError extends RuntimeError {
  constructor() {
    super(Subscription.AGGREGATE_NAME, SubscriptionErrorCodes.USER_NOT_FOUND, "User not found.");
  }
}
