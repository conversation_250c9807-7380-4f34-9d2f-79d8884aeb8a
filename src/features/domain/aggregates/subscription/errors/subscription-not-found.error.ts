import { RuntimeError } from "@heronjs/common";
import { Subscription } from "@features/domain/aggregates/subscription/subscription";
import { SubscriptionErrorCodes } from "@features/domain/aggregates/subscription/errors/errors";

export class SubscriptionNotFoundError extends RuntimeError {
  constructor() {
    super(
      Subscription.AGGREGATE_NAME,
      SubscriptionErrorCodes.SUBSCRIPTION_NOT_FOUND,
      "Subscription not found.",
    );
  }
}
