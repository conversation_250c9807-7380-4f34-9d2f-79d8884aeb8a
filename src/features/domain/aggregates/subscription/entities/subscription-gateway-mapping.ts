import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";
import { Entity, IEntity, EntityConstructorPayload } from "@cbidigital/aqua-ddd";

export type CreateSubscriptionGatewayMappingInput = {
  gateway: string;
  subscriptionId: string;
  subscriptionGatewayId: string;
};

export type UpdateSubscriptionGatewayMappingInput = {
  id: string;
  gateway?: string;
  subscriptionGatewayId?: string;
};

export type SubscriptionGatewayMappingProps = {
  gateway: string;
  subscriptionId: string;
  subscriptionGatewayId: string;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type SubscriptionGatewayMappingMethods = {
  create(input: CreateSubscriptionGatewayMappingInput): Promise<void>;
  update(input: UpdateSubscriptionGatewayMappingInput): Promise<void>;
};

export type ISubscriptionGatewayMapping = IEntity<
  SubscriptionGatewayMappingProps,
  SubscriptionGatewayMappingMethods
>;

export class SubscriptionGatewayMapping
  extends Entity<SubscriptionGatewayMappingProps, SubscriptionGatewayMappingMethods>
  implements ISubscriptionGatewayMapping
{
  static readonly ENTITY_NAME = "SubscriptionGatewayMapping";

  constructor(props?: EntityConstructorPayload<SubscriptionGatewayMappingProps>) {
    super(props);
  }

  /** Props **/
  get subscriptionId(): string {
    return this.props.subscriptionId;
  }

  get gateway(): string {
    return this.props.gateway;
  }

  get subscriptionGatewayId(): string {
    return this.props.subscriptionGatewayId;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/
  private setSubscriptionId(payload?: string) {
    if (payload !== undefined) this.setProp("subscriptionId", payload);
  }

  private setGateway(payload?: string) {
    if (payload !== undefined) this.setProp("gateway", payload);
  }

  private setSubscriptionGatewayId(payload?: string) {
    if (payload !== undefined) this.setProp("subscriptionGatewayId", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async create(payload: CreateSubscriptionGatewayMappingInput) {
    // handle logic
    this.setId(randomUUID());
    this.setGateway(payload.gateway);
    this.setSubscriptionId(payload.subscriptionId);
    this.setSubscriptionGatewayId(payload.subscriptionGatewayId);
    this.setCreatedAt(new Date());
  }

  async update(payload: UpdateSubscriptionGatewayMappingInput) {
    // handle logic
    this.setId(payload.id);
    this.setGateway(payload.gateway);
    this.setSubscriptionGatewayId(payload.subscriptionGatewayId);
    this.setUpdatedAt(new Date());
  }
}
