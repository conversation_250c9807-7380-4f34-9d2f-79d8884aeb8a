import { Entity, IEntity, EntityConstructorPayload } from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { TimestampUtil } from "@shared";
import { Nullable } from "@heronjs/common";
import { InvoiceStatusEnum } from "@features/domain/aggregates/subscription/enums";

export type CreateInvoiceInput = {
  subscriptionId: string;
  paymentMethodId?: string;
  periodStart?: Nullable<number>;
  periodEnd?: Nullable<number>;
  invoiceGateway: string;
};

export type UpdateInvoiceInput = {
  status?: InvoiceStatusEnum;
} & Partial<CreateInvoiceInput>;

export type InvoiceProps = {
  subscriptionId: string;
  paymentMethodId: Nullable<string>;
  periodStart: Nullable<number>;
  periodEnd: Nullable<number>;
  status: InvoiceStatusEnum;
  invoiceGateway: string;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type InvoiceMethods = {
  create(input: CreateInvoiceInput): Promise<void>;
  update(input: UpdateInvoiceInput): Promise<void>;
};

export type IInvoice = IEntity<InvoiceProps, InvoiceMethods>;
export class Invoice extends Entity<InvoiceProps, InvoiceMethods> implements IInvoice {
  constructor(props?: EntityConstructorPayload<InvoiceProps>) {
    super(props);
  }

  /** Props **/

  get subscriptionId(): string {
    return this.props.subscriptionId;
  }

  get paymentMethodId(): Nullable<string> {
    return this.props.paymentMethodId;
  }

  get periodStart(): Nullable<number> {
    return this.props.periodStart;
  }

  get periodEnd(): Nullable<number> {
    return this.props.periodEnd;
  }

  get status(): InvoiceStatusEnum {
    return this.props.status;
  }

  get invoiceGateway(): string {
    return this.props.invoiceGateway;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/
  private setSubscriptionId(payload?: string) {
    if (payload !== undefined) this.setProp("subscriptionId", payload);
  }

  private setPaymentMethodId(payload?: Nullable<string>) {
    if (payload !== undefined) this.setProp("paymentMethodId", payload);
  }

  private setPeriodStart(payload?: Nullable<number>) {
    const value = TimestampUtil.convertSecToMs(payload ?? Date.now());
    if (payload !== undefined) this.setProp("periodStart", value);
  }

  private setPeriodEnd(payload?: Nullable<number>) {
    const value = TimestampUtil.convertSecToMs(payload ?? Date.now());
    if (payload !== undefined) this.setProp("periodEnd", value);
  }

  private setStatus(payload?: InvoiceStatusEnum) {
    if (payload !== undefined) this.setProp("status", payload);
  }

  private setInvoiceGateway(payload?: string) {
    if (payload !== undefined) this.setProp("invoiceGateway", payload);
  }

  private setCreatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async create(payload: CreateInvoiceInput): Promise<void> {
    this.setId(randomUUID());
    this.setSubscriptionId(payload.subscriptionId);
    this.setPeriodStart(payload.periodStart);
    this.setPeriodEnd(payload.periodEnd);
    this.setPaymentMethodId(payload.paymentMethodId);
    this.setInvoiceGateway(payload.invoiceGateway);
    this.setStatus(InvoiceStatusEnum.PENDING);
    this.setCreatedAt(new Date());
  }

  async update(payload: UpdateInvoiceInput): Promise<void> {
    this.setStatus(payload.status);
    this.setPeriodStart(payload.periodStart);
    this.setPeriodEnd(payload.periodEnd);
    this.setUpdatedAt(new Date());
  }
}
