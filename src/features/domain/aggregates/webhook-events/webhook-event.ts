import {
  AggregateRoot,
  IAggregateRoot,
  AggregateRootConstructorPayload,
} from "@cbidigital/aqua-ddd";
import { randomUUID } from "crypto";
import { Nullable } from "@heronjs/common";

export type CreateWebhookEventInput = {
  gateway: string;
  eventGatewayId: string;
  eventType: string;
  payload: Record<string, any>;
};

export type UpdateWebhookEventInput = { id: string } & Partial<CreateWebhookEventInput>;

export type WebhookEventProps = {
  gateway: string;
  eventGatewayId: string;
  eventType: string;
  payload: Record<string, any>;
  receivedAt: Date;
  processedAt: Nullable<Date>;
};

export type WebhookEventMethods = {
  create(input: CreateWebhookEventInput): Promise<void>;
  update(input: UpdateWebhookEventInput): Promise<void>;
};

export type IWebhookEvent = IAggregateRoot<WebhookEventProps, WebhookEventMethods>;

export class WebhookEvent
  extends AggregateRoot<WebhookEventProps, WebhookEventMethods>
  implements IWebhookEvent
{
  static readonly AGGREGATE_NAME = "capability";

  constructor(props: AggregateRootConstructorPayload<WebhookEventProps>) {
    super(props);
  }

  /** Props **/
  get gateway(): string {
    return this.props.gateway;
  }

  get eventGatewayId(): string {
    return this.props.eventGatewayId;
  }

  get eventType(): string {
    return this.props.eventType;
  }

  get payload(): Record<string, any> {
    return this.props.payload;
  }

  get receivedAt(): Date {
    return this.props.receivedAt;
  }

  get processedAt(): Nullable<Date> {
    return this.props.processedAt;
  }

  /** Methods **/
  private setGateway(payload?: string) {
    if (payload !== undefined) this.setProp("gateway", payload);
  }

  private setEventGatewayId(payload?: string) {
    if (payload !== undefined) this.setProp("eventGatewayId", payload);
  }

  private setEventType(payload?: string) {
    if (payload !== undefined) this.setProp("eventType", payload);
  }

  private setPayload(payload?: Record<string, any>) {
    if (payload !== undefined) this.setProp("payload", payload);
  }

  private setReceivedAt(payload?: Date) {
    if (payload !== undefined) this.setProp("receivedAt", payload);
  }

  private setProcessedAt(payload?: Nullable<Date>) {
    if (payload !== undefined) this.setProp("processedAt", payload);
  }

  async create(payload: CreateWebhookEventInput) {
    this.setId(randomUUID());
    this.setGateway(payload.gateway);
    this.setEventGatewayId(payload.eventGatewayId);
    this.setEventType(payload.eventType);
    this.setPayload(payload.payload);
    this.setReceivedAt(new Date());
  }

  async update(payload: UpdateWebhookEventInput) {
    this.setId(payload.id);
    this.setGateway(payload.gateway);
    this.setEventGatewayId(payload.eventGatewayId);
    this.setEventType(payload.eventType);
    this.setPayload(payload.payload);
    this.setProcessedAt(new Date());
  }
}
