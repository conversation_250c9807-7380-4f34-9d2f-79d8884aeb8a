import { Nullable } from "@heronjs/common";
import { MembershipCycleEnum } from "@features/domain/aggregates";
import { MembershipCycleGatewayMappingDto } from "@features/domain/dtos/membership-cycle-gateway-mapping.dto";

export type MembershipCycleDto = {
  id: string;
  membershipId: string;
  billingCycle: MembershipCycleEnum;
  amount: number;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
  gatewayMappings?: MembershipCycleGatewayMappingDto[];
};
