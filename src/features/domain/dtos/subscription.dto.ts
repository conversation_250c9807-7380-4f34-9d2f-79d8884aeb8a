import { Nullable } from "@heronjs/common";
import { InvoiceDto } from "./invoice.dto";
import { PaymentGatewayProvider } from "@features/domain/services";
import { MembershipDto } from "@features/domain/dtos/membership.dto";
import { MembershipCycleDto } from "@features/domain/dtos/membership-cycle.dto";
import { SubscriptionStatusEnum } from "@features/domain/aggregates/subscription/enums";
import { SubscriptionGatewayMappingDto } from "@features/domain/dtos/subscription-gateway-mapping.dto";

export type SubscriptionDto = {
  id: string;
  tenantId: string;
  membershipId: string;
  membershipCycleId: string;
  paymentMethodId: Nullable<string>;
  status: SubscriptionStatusEnum;
  trialEnd: Nullable<number>;
  gateway: Nullable<PaymentGatewayProvider>;
  createdAt: Date;
  createdBy: string;
  updatedAt: Nullable<Date>;
  updatedBy: Nullable<string>;
  invoices: InvoiceDto[];
  membership?: MembershipDto;
  membershipCycle?: MembershipCycleDto;
  subscriptionGatewayMappings?: SubscriptionGatewayMappingDto[];
};
