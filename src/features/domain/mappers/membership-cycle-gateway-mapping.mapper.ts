import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { MembershipCycleGatewayMappingDto } from "@features/domain/dtos";
import {
  MembershipCycleGatewayMapping,
  IMembershipCycleGatewayMapping,
} from "@features/domain/aggregates";

export type IMembershipCycleGatewayMappingMapper = IMapper<
  MembershipCycleGatewayMappingDto,
  IMembershipCycleGatewayMapping
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CYCLE_GATEWAY_MAPPING,
  scope: Lifecycle.Singleton,
})
export class MembershipCycleGatewayMappingMapper
  extends BaseMapper
  implements IMembershipCycleGatewayMappingMapper
{
  constructor() {
    super();
  }

  async fromEntityToDto(
    entity: IMembershipCycleGatewayMapping,
  ): Promise<MembershipCycleGatewayMappingDto> {
    return {
      id: entity.id,
      membershipCycleId: entity.membershipCycleId,
      membershipCycleGatewayId: entity.membershipCycleGatewayId,
      gateway: entity.gateway,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(
    dto: MembershipCycleGatewayMappingDto,
  ): Promise<IMembershipCycleGatewayMapping> {
    return new MembershipCycleGatewayMapping({
      id: dto.id,
      props: {
        membershipCycleId: dto.membershipCycleId,
        membershipCycleGatewayId: dto.membershipCycleGatewayId,
        gateway: dto.gateway,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }

  async fromEntitiesToDtos(
    entities: IMembershipCycleGatewayMapping[],
  ): Promise<MembershipCycleGatewayMappingDto[]> {
    return Promise.all(entities.map((entity) => this.fromEntityToDto(entity)));
  }

  async fromDtosToEntities(
    dtos: MembershipCycleGatewayMappingDto[],
  ): Promise<IMembershipCycleGatewayMapping[]> {
    return Promise.all(dtos.map((dto) => this.fromDtoToEntity(dto)));
  }
}
