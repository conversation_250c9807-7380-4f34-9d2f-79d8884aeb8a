import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { SubscriptionDto } from "@features/domain/dtos";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { Subscription, ISubscription } from "@features/domain/aggregates";
import { IInvoiceMapper } from "@features/domain/mappers/invoice.mapper";
import { ISubscriptionGatewayMappingMapper } from "@features/domain/mappers/subscription-gateway-mapping.mapper";

export type ISubscriptionMapper = IMapper<SubscriptionDto, ISubscription>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.SUBSCRIPTION,
  scope: Lifecycle.Singleton,
})
export class SubscriptionMapper extends BaseMapper implements ISubscriptionMapper {
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.INVOICE)
    protected readonly invoiceMapper: IInvoiceMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.SUBSCRIPTION_GATEWAY_MAPPING)
    protected readonly subsGatewayMappingMapper: ISubscriptionGatewayMappingMapper,
  ) {
    super();
  }

  async fromEntityToDto(entity: ISubscription): Promise<SubscriptionDto> {
    const invoices = await this.invoiceMapper.fromEntitiesToDtos(entity.invoices);

    return {
      id: entity.id,
      tenantId: entity.tenantId,
      membershipId: entity.membershipId,
      paymentMethodId: entity.paymentMethodId,
      membershipCycleId: entity.membershipCycleId,
      gateway: entity.gateway,
      status: entity.status,
      trialEnd: entity.trialEnd,
      createdAt: entity.createdAt,
      createdBy: entity.createdBy,
      updatedAt: entity.updatedAt,
      updatedBy: entity.updatedBy,
      invoices,
    };
  }

  async fromDtoToEntity(dto: SubscriptionDto): Promise<ISubscription> {
    // Map membership changes from DTO to entities
    const invoices = dto.invoices ? await this.invoiceMapper.fromDtosToEntities(dto.invoices) : [];

    const subscriptionGatewayMappings = await this.subsGatewayMappingMapper.fromDtosToEntities(
      dto.subscriptionGatewayMappings ?? [],
    );

    const subscription = new Subscription({
      id: dto.id,
      props: {
        tenantId: dto.tenantId,
        membershipId: dto.membershipId,
        membershipCycleId: dto.membershipCycleId,
        paymentMethodId: dto.paymentMethodId,
        status: dto.status,
        gateway: dto.gateway,
        trialEnd: dto.trialEnd,
        createdAt: dto.createdAt,
        createdBy: dto.createdBy,
        updatedAt: dto.updatedAt,
        updatedBy: dto.updatedBy,
        invoices,
        subscriptionGatewayMappings,
      },
    });

    return subscription;
  }
}
