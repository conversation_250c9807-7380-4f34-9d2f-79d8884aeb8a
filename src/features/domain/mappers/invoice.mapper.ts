import { InvoiceDto } from "@features/domain/dtos";
import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { Invoice, IInvoice } from "@features/domain/aggregates";

export type IInvoiceMapper = IMapper<InvoiceDto, IInvoice>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.INVOICE,
  scope: Lifecycle.Singleton,
})
export class InvoiceMapper extends BaseMapper implements IInvoiceMapper {
  constructor() {
    super();
  }

  async fromEntityToDto(entity: IInvoice): Promise<InvoiceDto> {
    return {
      id: entity.id,
      subscriptionId: entity.subscriptionId,
      paymentMethodId: entity.paymentMethodId,
      periodStart: entity.periodStart,
      periodEnd: entity.periodEnd,
      status: entity.status,
      invoiceGateway: entity.invoiceGateway,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(dto: InvoiceDto): Promise<IInvoice> {
    return new Invoice({
      id: dto.id,
      props: {
        subscriptionId: dto.subscriptionId,
        paymentMethodId: dto.paymentMethodId,
        periodStart: dto.periodStart,
        periodEnd: dto.periodEnd,
        status: dto.status,
        invoiceGateway: dto.invoiceGateway,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
