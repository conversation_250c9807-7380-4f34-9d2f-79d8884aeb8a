import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { CapabilityDto } from "@features/domain/dtos";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { Capability, ICapability } from "@features/domain/aggregates";

export type ICapabilityMapper = IMapper<CapabilityDto, ICapability>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.CAPABILITY,
  scope: Lifecycle.Singleton,
})
export class CapabilityMapper extends BaseMapper implements ICapabilityMapper {
  constructor() {
    super();
  }

  async fromEntityToDto(entity: ICapability): Promise<CapabilityDto> {
    return {
      id: entity.id,
      key: entity.key,
      description: entity.description,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(dto: CapabilityDto): Promise<ICapability> {
    return new Capability({
      id: dto.id,
      props: {
        key: dto.key,
        description: dto.description,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
