import {
  SubscriptionGatewayMapping,
  ISubscriptionGatewayMapping,
} from "@features/domain/aggregates";
import { Lifecycle, Provider } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { SubscriptionGatewayMappingDto } from "@features/domain";

export type ISubscriptionGatewayMappingMapper = IMapper<
  SubscriptionGatewayMappingDto,
  ISubscriptionGatewayMapping
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.MAPPER.SUBSCRIPTION_GATEWAY_MAPPING,
  scope: Lifecycle.Singleton,
})
export class SubscriptionGatewayMappingMapper
  extends BaseMapper
  implements ISubscriptionGatewayMappingMapper
{
  constructor() {
    super();
  }

  async fromEntityToDto(
    entity: ISubscriptionGatewayMapping,
  ): Promise<SubscriptionGatewayMappingDto> {
    return {
      id: entity.id,
      subscriptionId: entity.subscriptionId,
      subscriptionGatewayId: entity.subscriptionGatewayId,
      gateway: entity.gateway,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(dto: SubscriptionGatewayMappingDto): Promise<ISubscriptionGatewayMapping> {
    return new SubscriptionGatewayMapping({
      id: dto.id,
      props: {
        subscriptionId: dto.subscriptionId,
        subscriptionGatewayId: dto.subscriptionGatewayId,
        gateway: dto.gateway,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
