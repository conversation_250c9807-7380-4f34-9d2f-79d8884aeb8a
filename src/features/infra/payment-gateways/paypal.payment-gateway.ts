import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Lifecycle, Provider } from "@heronjs/common";
import {
  IPaymentGateway,
  PaymentGatewayPrice,
  PaymentGatewayProduct,
  PaymentGatewaySubscription,
  CreatePaymentGatewayPriceInput,
  CreatePaymentGatewayProductInput,
  CreatePaymentGatewayCustomerInput,
  UpdatePaymentGatewayCustomerInput,
  CreatePaymentGatewayCustomerOutput,
  UpdatePaymentGatewayCustomerOutput,
  CancelPaymentGatewaySubscriptionInput,
  CreatePaymentGatewaySubscriptionInput,
  UpdatePaymentGatewaySubscriptionInput,
  CancelPaymentGatewaySubscriptionOutput,
  UpdatePaymentGatewaySubscriptionOutput,
} from "@features/domain";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.PAYMENT_GATEWAY.PAYPAL,
  scope: Lifecycle.Singleton,
})
export class PaypalPaymentGateway implements IPaymentGateway {
  getSubscription(subscriptionId: string): Promise<any> {
    throw new Error("Method not implemented.");
  }
  getInvoice(invoiceId: string): Promise<any> {
    throw new Error("Method not implemented.");
  }
  parseEvent(input: any): Promise<any> {
    throw new Error("Method not implemented.");
  }
  createProduct(input: CreatePaymentGatewayProductInput): Promise<PaymentGatewayProduct> {
    throw new Error("Method not implemented.");
  }
  createPrice(input: CreatePaymentGatewayPriceInput): Promise<PaymentGatewayPrice> {
    throw new Error("Method not implemented.");
  }
  createSubscription(
    input: CreatePaymentGatewaySubscriptionInput,
  ): Promise<PaymentGatewaySubscription> {
    throw new Error("Method not implemented.");
  }
  updateSubscription(
    input: UpdatePaymentGatewaySubscriptionInput,
  ): Promise<UpdatePaymentGatewaySubscriptionOutput> {
    throw new Error("Method not implemented.");
  }
  cancelSubscription(
    input: CancelPaymentGatewaySubscriptionInput,
  ): Promise<CancelPaymentGatewaySubscriptionOutput> {
    throw new Error("Method not implemented.");
  }
  createCustomer(
    input: CreatePaymentGatewayCustomerInput,
  ): Promise<CreatePaymentGatewayCustomerOutput> {
    throw new Error("Method not implemented.");
  }
  updateCustomer(
    input: UpdatePaymentGatewayCustomerInput,
  ): Promise<UpdatePaymentGatewayCustomerOutput> {
    throw new Error("Method not implemented.");
  }
}
