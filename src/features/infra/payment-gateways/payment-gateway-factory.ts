import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { IPaymentGateway, IPaymentGatewayFactory, PaymentGatewayProvider } from "@features/domain";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY,
  scope: Lifecycle.Singleton,
})
export class PaymentGatewayFactory implements IPaymentGatewayFactory {
  private readonly logger = new Logger(this.constructor.name);
  private readonly paymentGatewayMap: Map<PaymentGatewayProvider, IPaymentGateway>;

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.PAYMENT_GATEWAY.STRIPE)
    private readonly stripePaymentGateway: IPaymentGateway,
    @Inject(MEMBERSHIP_INJECT_TOKENS.PAYMENT_GATEWAY.PAYPAL)
    private readonly paypalPaymentGateway: IPaymentGateway,
  ) {
    // Initialize map in constructor to avoid recreating the Map each time get() is called
    this.paymentGatewayMap = new Map([
      [PaymentGatewayProvider.STRIPE, this.stripePaymentGateway],
      [PaymentGatewayProvider.PAYPAL, this.paypalPaymentGateway],
    ]);
  }

  get(provider: PaymentGatewayProvider): IPaymentGateway {
    const paymentGateway = this.paymentGatewayMap.get(provider);
    if (!paymentGateway) {
      throw new Error(`Payment gateway for provider "${provider}" not found`);
    }
    this.logger.info(`Using ${provider} payment gateway`);
    return paymentGateway;
  }
}
