import { K<PERSON> } from "knex";
import { IDatabase } from "@cbidigital/aqua-ddd";
import { IDatabaseUtil } from "@features/domain";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { DataSource, Lifecycle, Provider } from "@heronjs/common";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE,
  scope: Lifecycle.Singleton,
})
export class DatabaseUtil implements IDatabaseUtil {
  private readonly _db: IDatabase;

  constructor(@DataSource() db: IDatabase) {
    this._db = db;
  }

  async startTrx(): Promise<Knex.Transaction> {
    const client = this._db.database();
    if (!client) throw new Error();
    const trx = await client?.transaction();
    return trx;
  }
}
