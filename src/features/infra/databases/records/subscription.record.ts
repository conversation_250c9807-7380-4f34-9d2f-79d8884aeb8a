import { Nullable } from "@heronjs/common";
import { PaymentGatewayProvider } from "@features/domain";
import { InvoiceRecord } from "./invoice.record";
import { MembershipRecord } from "@features/infra/databases/records/membership.record";
import { SubscriptionStatusEnum } from "@features/domain/aggregates/subscription/enums";
import { MembershipCycleRecord } from "@features/infra/databases/records/membership-cycle.record";
import { SubscriptionGatewayMappingRecord } from "@features/infra/databases/records/subscription-gateway-mapping.record";

export type SubscriptionRecord = {
  id: string;
  tenant_id: string;
  membership_id: string;
  membership_cycle_id: string;
  payment_method_id: Nullable<string>;
  status: SubscriptionStatusEnum;
  gateway: Nullable<PaymentGatewayProvider>;
  trial_end: Nullable<number>;
  created_at: Date;
  created_by: string;
  updated_at: Nullable<Date>;
  updated_by: Nullable<string>;
  billing_cycles?: InvoiceRecord[];
  membership?: MembershipRecord;
  membership_cycle?: MembershipCycleRecord;
  subscription_gateway_mappings?: SubscriptionGatewayMappingRecord[];
};
