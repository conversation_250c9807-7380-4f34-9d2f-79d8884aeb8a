import { MembershipCycleRecord, MembershipCapabilityRecord } from "@features/infra/databases";
import { Nullable } from "@heronjs/common";
import { MembershipStatusEnum } from "@features/domain";

export type MembershipRecord = {
  id: string;
  tenant_id: Nullable<string>;
  name: string;
  is_custom: boolean;
  is_trial: boolean;
  status: MembershipStatusEnum;
  description: string;
  cycles: Nullable<MembershipCycleRecord[]>;
  capabilities: Nullable<MembershipCapabilityRecord[]>;
  created_at: Date;
  created_by: string;
  updated_at: Nullable<Date>;
  updated_by: Nullable<string>;
};
