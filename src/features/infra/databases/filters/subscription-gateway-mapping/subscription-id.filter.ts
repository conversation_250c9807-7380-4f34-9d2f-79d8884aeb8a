import { MEMBERSHIP_MODULE_TABLE_NAMES } from "@constants";
import { Filter, FilterPayload, FilterTypes } from "@cbidigital/aqua-ddd";

export class SubscriptionIdFilter extends Filter {
  constructor(payload: FilterPayload) {
    super({
      type: FilterTypes.STRING,
      compareField: `${MEMBERSHIP_MODULE_TABLE_NAMES.SUBSCRIPTION_GATEWAY_MAPPING}.subscription_id`,
      ...payload,
    });
  }
}
