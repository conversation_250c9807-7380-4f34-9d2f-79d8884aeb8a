import { MembershipCycleGatewayMappingDto } from "@features/domain";
import { BaseRecordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";
import { MembershipCycleGatewayMappingRecord } from "@features/infra/databases/records";

export type IMembershipCycleGatewayMappingRecordMapper = IRecordMapper<
  MembershipCycleGatewayMappingDto,
  MembershipCycleGatewayMappingRecord
>;

export class MembershipCycleGatewayMappingRecordMapper
  extends BaseRecordMapper
  implements IMembershipCycleGatewayMappingRecordMapper
{
  fromRecordToDto(
    record: Partial<MembershipCycleGatewayMappingRecord>,
  ): Partial<MembershipCycleGatewayMappingDto> {
    return {
      id: record.id,
      membershipCycleId: record.membership_cycle_id,
      membershipCycleGatewayId: record.membership_cycle_gateway_id,
      gateway: record.gateway,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(
    dto: Partial<MembershipCycleGatewayMappingDto>,
  ): Partial<MembershipCycleGatewayMappingRecord> {
    return {
      id: dto.id,
      membership_cycle_id: dto.membershipCycleId,
      membership_cycle_gateway_id: dto.membershipCycleGatewayId,
      gateway: dto.gateway,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }

  fromRecordsToDtos(
    records: Partial<MembershipCycleGatewayMappingRecord>[],
  ): Partial<MembershipCycleGatewayMappingDto>[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(
    dtos: Partial<MembershipCycleGatewayMappingDto>[],
  ): Partial<MembershipCycleGatewayMappingRecord>[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
