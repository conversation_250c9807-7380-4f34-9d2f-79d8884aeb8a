import { BaseRecordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";
import { InvoiceRecordMapper } from "./invoice.record-mapper";
import { SubscriptionRecord } from "@features/infra/databases/records";
import { MembershipRecordMapper } from "@features/infra/databases/record-mappers/membership.record-mapper";
import { MembershipCycleRecordMapper } from "@features/infra/databases/record-mappers/membership-cycle.record-mapper";
import { SubscriptionGatewayMappingRecordMapper } from "@features/infra/databases/record-mappers/subscription-gateway-mapping.record-mapper";
import {
  MembershipDto,
  InvoiceDto,
  SubscriptionDto,
  MembershipCycleDto,
  SubscriptionGatewayMappingDto,
} from "@features/domain";

export type ISubscriptionRecordMapper = IRecordMapper<SubscriptionDto, SubscriptionRecord>;

export class SubscriptionRecordMapper
  extends BaseRecordMapper
  implements ISubscriptionRecordMapper
{
  private membershipRecordMapper = new MembershipRecordMapper();
  private billingCycleRecordMapper = new InvoiceRecordMapper();
  private membershipCycleRecordMapper = new MembershipCycleRecordMapper();
  private subsGatewayMappingRecordMapper = new SubscriptionGatewayMappingRecordMapper();

  fromRecordToDto(record: Partial<SubscriptionRecord>): Partial<SubscriptionDto> {
    const billingCycles = record.billing_cycles
      ? this.billingCycleRecordMapper.fromRecordsToDtos(record.billing_cycles)
      : null;
    const membership = record.membership
      ? this.membershipRecordMapper.fromRecordToDto(record.membership)
      : null;
    const membershipCycle = record.membership_cycle
      ? this.membershipCycleRecordMapper.fromRecordToDto(record.membership_cycle)
      : null;

    const gatewayMappings = record.subscription_gateway_mappings
      ? record.subscription_gateway_mappings.map((mappingRecord) =>
          this.subsGatewayMappingRecordMapper.fromRecordToDto(mappingRecord),
        )
      : undefined;

    return {
      id: record.id,
      tenantId: record.tenant_id,
      membershipId: record.membership_id,
      paymentMethodId: record.payment_method_id,
      membershipCycleId: record.membership_cycle_id,
      status: record.status,
      trialEnd: record.trial_end,
      createdAt: record.created_at,
      createdBy: record.created_by,
      updatedAt: record.updated_at,
      updatedBy: record.updated_by,
      membership: membership as MembershipDto,
      membershipCycle: membershipCycle as MembershipCycleDto,
      invoices: billingCycles as InvoiceDto[],
      subscriptionGatewayMappings: gatewayMappings as SubscriptionGatewayMappingDto[],
    };
  }

  fromDtoToRecord(dto: Partial<SubscriptionDto>): Partial<SubscriptionRecord> {
    return {
      id: dto.id,
      tenant_id: dto.tenantId,
      membership_id: dto.membershipId,
      payment_method_id: dto.paymentMethodId,
      membership_cycle_id: dto.membershipCycleId,
      status: dto.status,
      trial_end: dto.trialEnd,
      created_at: dto.createdAt,
      created_by: dto.createdBy,
      updated_at: dto.updatedAt,
      updated_by: dto.updatedBy,
    };
  }
}
