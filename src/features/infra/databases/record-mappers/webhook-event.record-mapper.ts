import { WebhookEventDto } from "@features/domain";
import { IR<PERSON>ordMapper } from "@cbidigital/aqua-ddd";
import { WebhookEventRecord } from "@features/infra/databases/records";

export class WebhookEventRecordMapper
  implements IRecordMapper<WebhookEventDto, WebhookEventRecord>
{
  fromRecordToDto(record: WebhookEventRecord): WebhookEventDto {
    return {
      id: record.id,
      gateway: record.gateway,
      eventGatewayId: record.event_gateway_id,
      eventType: record.event_type,
      payload: record.payload,
      receivedAt: record.received_at,
      processedAt: record.processed_at,
    };
  }

  fromDtoToRecord(dto: WebhookEventDto): WebhookEventRecord {
    return {
      id: dto.id,
      gateway: dto.gateway,
      event_gateway_id: dto.eventGatewayId,
      event_type: dto.eventType,
      payload: dto.payload,
      received_at: dto.receivedAt,
      processed_at: dto.processedAt,
    };
  }

  fromRecordsToDtos(records: WebhookEventRecord[]): WebhookEventDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(dtos: WebhookEventDto[]): WebhookEventRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
