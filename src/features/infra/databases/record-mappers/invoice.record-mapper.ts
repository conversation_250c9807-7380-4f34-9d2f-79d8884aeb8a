import { InvoiceDto } from "@features/domain";
import { InvoiceRecord } from "@features/infra/databases/records";
import { Base<PERSON><PERSON>ordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";

export type IInvoiceRecordMapper = IRecordMapper<InvoiceDto, InvoiceRecord>;

export class InvoiceR<PERSON>ordMapper extends BaseR<PERSON>ord<PERSON>apper implements IInvoiceRecordMapper {
  fromRecordToDto(record: Partial<InvoiceRecord>): Partial<InvoiceDto> {
    return {
      id: record.id,
      subscriptionId: record.subscription_id,
      paymentMethodId: record.payment_method_id,
      status: record.status,
      periodStart: record.period_start,
      periodEnd: record.period_end,
      invoiceGateway: record.invoice_gateway,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(dto: Partial<InvoiceDto>): Partial<InvoiceRecord> {
    return {
      id: dto.id,
      subscription_id: dto.subscriptionId,
      payment_method_id: dto.paymentMethodId,
      period_start: dto.periodStart,
      period_end: dto.periodEnd,
      status: dto.status,
      invoice_gateway: dto.invoiceGateway,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }
}
