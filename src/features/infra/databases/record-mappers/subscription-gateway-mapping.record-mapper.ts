import { SubscriptionGatewayMappingDto } from "@features/domain";
import { BaseRecordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";
import { SubscriptionGatewayMappingRecord } from "@features/infra/databases/records";

export type ISubscriptionGatewayMappingRecordMapper = IRecordMapper<
  SubscriptionGatewayMappingDto,
  SubscriptionGatewayMappingRecord
>;

export class SubscriptionGatewayMappingRecordMapper
  extends BaseR<PERSON>ordMapper
  implements ISubscriptionGatewayMappingRecordMapper
{
  constructor() {
    super();
  }

  fromRecordToDto(
    record: Partial<SubscriptionGatewayMappingRecord>,
  ): Partial<SubscriptionGatewayMappingDto> {
    const dto: Partial<SubscriptionGatewayMappingDto> = {
      id: record.id,
      subscriptionId: record.subscription_id,
      subscriptionGatewayId: record.subscription_gateway_id,
      gateway: record.gateway,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
    return dto;
  }

  fromDtoToRecord(
    dto: Partial<SubscriptionGatewayMappingDto>,
  ): Partial<SubscriptionGatewayMappingRecord> {
    return {
      id: dto.id,
      subscription_id: dto.subscriptionId,
      subscription_gateway_id: dto.subscriptionGatewayId,
      gateway: dto.gateway,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }
}
