import { BaseR<PERSON>ordMapper, I<PERSON><PERSON>ordMapper } from "@cbidigital/aqua-ddd";
import { MembershipCycleRecord } from "@features/infra/databases/records";
import { MembershipCycleDto, MembershipCycleGatewayMappingDto } from "@features/domain";
import { MembershipCycleGatewayMappingRecordMapper } from "@features/infra/databases/record-mappers/membership-cycle-gateway-mapping.record-mapper";

export type IMembershipCycleRecordMapper = IRecordMapper<MembershipCycleDto, MembershipCycleRecord>;
export class MembershipCycleRecordMapper
  extends BaseRecordMapper
  implements IMembershipCycleRecordMapper
{
  private readonly membershipCycleGatewayMappingMapper =
    new MembershipCycleGatewayMappingRecordMapper();

  fromRecordToDto(record: Partial<MembershipCycleRecord>): Partial<MembershipCycleDto> {
    const gatewayMappings = record.gateway_mappings
      ? this.membershipCycleGatewayMappingMapper.fromRecordsToDtos(record.gateway_mappings)
      : undefined;
    return {
      id: record.id,
      membershipId: record.membership_id,
      amount: record.amount,
      billingCycle: record.billing_cycle,
      createdAt: record.created_at,
      createdBy: record.created_by,
      updatedAt: record.updated_at,
      updatedBy: record.updated_by,
      gatewayMappings: gatewayMappings as MembershipCycleGatewayMappingDto[],
    };
  }

  fromDtoToRecord(dto: Partial<MembershipCycleDto>): Partial<MembershipCycleRecord> {
    return {
      id: dto.id,
      membership_id: dto.membershipId,
      amount: dto.amount,
      billing_cycle: dto.billingCycle,
      created_at: dto.createdAt,
      created_by: dto.createdBy,
      updated_at: dto.updatedAt,
      updated_by: dto.updatedBy,
    };
  }
}
