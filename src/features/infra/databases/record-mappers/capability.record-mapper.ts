import { CapabilityDto } from "@features/domain";
import { IRecordMapper } from "@cbidigital/aqua-ddd";
import { CapabilityRecord } from "@features/infra/databases/records";

export class CapabilityRecordMapper implements IRecordMapper<CapabilityDto, CapabilityRecord> {
  fromRecordToDto(record: CapabilityRecord): CapabilityDto {
    return {
      id: record.id,
      key: record.key,
      description: record.description,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(dto: CapabilityDto): CapabilityRecord {
    return {
      id: dto.id,
      key: dto.key,
      description: dto.description,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }

  fromRecordsToDtos(records: CapabilityRecord[]): CapabilityDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(dtos: CapabilityDto[]): CapabilityRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
