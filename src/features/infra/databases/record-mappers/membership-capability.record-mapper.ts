import { I<PERSON>ecordMapper } from "@cbidigital/aqua-ddd";
import { MembershipCapabilityDto } from "@features/domain";
import { MembershipCapabilityRecord } from "@features/infra/databases/records";

export class MembershipCapabilityRecordMapper
  implements IRecordMapper<MembershipCapabilityDto, MembershipCapabilityRecord>
{
  fromRecordToDto(record: MembershipCapabilityRecord): MembershipCapabilityDto {
    const dto: MembershipCapabilityDto = {
      id: record.id,
      membershipId: record.membership_id,
      capabilityId: record.capability_id,
      value: record.value,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };

    return dto;
  }

  fromDtoToRecord(dto: MembershipCapabilityDto): MembershipCapabilityRecord {
    const record: MembershipCapabilityRecord = {
      id: dto.id,
      membership_id: dto.membershipId,
      capability_id: dto.capabilityId,
      value: dto.value,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };

    return record;
  }

  fromRecordsToDtos(records: MembershipCapabilityRecord[]): MembershipCapabilityDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(dtos: MembershipCapabilityDto[]): MembershipCapabilityRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
