import { MembershipGatewayMappingDto } from "@features/domain";
import { BaseRecordMapper, IRecordMapper } from "@cbidigital/aqua-ddd";
import { MembershipGatewayMappingRecord } from "@features/infra/databases/records";

export type IMembershipGatewayMappingRecordMapper = IRecordMapper<
  MembershipGatewayMappingDto,
  MembershipGatewayMappingRecord
>;

export class MembershipGatewayMappingRecordMapper
  extends BaseRecordMapper
  implements IMembershipGatewayMappingRecordMapper
{
  constructor() {
    super();
  }

  fromRecordToDto(
    record: Partial<MembershipGatewayMappingRecord>,
  ): Partial<MembershipGatewayMappingDto> {
    const dto: Partial<MembershipGatewayMappingDto> = {
      id: record.id,
      membershipId: record.membership_id,
      membershipGatewayId: record.membership_gateway_id,
      gateway: record.gateway,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
    return dto;
  }

  fromDtoToRecord(
    dto: Partial<MembershipGatewayMappingDto>,
  ): Partial<MembershipGatewayMappingRecord> {
    return {
      id: dto.id,
      membership_id: dto.membershipId,
      membership_gateway_id: dto.membershipGatewayId,
      gateway: dto.gateway,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }
}
