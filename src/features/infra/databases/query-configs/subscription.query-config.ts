import { QueryConfig } from "@cbidigital/aqua-ddd";
import { SubscriptionDto } from "@features/domain";
import {
  TenantIdFilter,
  SubscriptionIdFilter,
  SubsMembershipCycleIdFilter,
  SubscriptionStatusFilter,
} from "@features/infra/databases/filters";

export const SubscriptionQueryConfig: QueryConfig<keyof SubscriptionDto> = {
  id: {
    sortable: true,
    filterable: true,
    filterClass: SubscriptionIdFilter,
  },

  tenantId: {
    sortable: true,
    filterable: true,
    filterClass: TenantIdFilter,
  },

  membershipCycleId: {
    sortable: true,
    filterable: true,
    filterClass: SubsMembershipCycleIdFilter,
  },

  status: {
    sortable: true,
    filterable: true,
    filterClass: SubscriptionStatusFilter,
  },
};
