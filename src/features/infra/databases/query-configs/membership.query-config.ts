import { MembershipDto } from "@features/domain";
import { QueryConfig } from "@cbidigital/aqua-ddd";
import {
  IsCustomFilter,
  MembershipIdFilter,
  IsTrialFilterFilter,
  MembershipNameFilter,
  MembershipStatusFilter,
  SubsMembershipCycleIdFilter,
  MembershipCycleTenantIdFilter,
} from "@features/infra/databases/filters";

export const MembershipQueryConfig: QueryConfig<
  keyof MembershipDto | "tenantId" | "membershipCycleId"
> = {
  id: {
    sortable: true,
    filterable: true,
    filterClass: MembershipIdFilter,
  },

  name: {
    sortable: true,
    filterable: true,
    filterClass: MembershipNameFilter,
  },

  isCustom: {
    sortable: true,
    filterable: true,
    filterClass: IsCustomFilter,
  },

  isTrial: {
    sortable: true,
    filterable: true,
    filterClass: IsTrialFilterFilter,
  },

  status: {
    sortable: true,
    filterable: true,
    filterClass: MembershipStatusFilter,
  },

  tenantId: {
    sortable: true,
    filterable: true,
    filterClass: MembershipCycleTenantIdFilter,
  },

  membershipCycleId: {
    sortable: true,
    filterable: true,
    filterClass: SubsMembershipCycleIdFilter,
  },
};
