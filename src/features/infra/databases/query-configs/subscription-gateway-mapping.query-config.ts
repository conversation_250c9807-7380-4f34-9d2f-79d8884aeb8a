import { QueryConfig } from "@cbidigital/aqua-ddd";
import { SubscriptionGatewayMappingDto } from "@features/domain";
import {
  SubscriptionIdFilter,
  SubscriptionGatewayIdFilter,
  SubscriptionGatewayMappingIdFilter,
} from "@features/infra/databases/filters/subscription-gateway-mapping";

export const SubscriptionGatewayMappingQueryConfig: QueryConfig<
  keyof SubscriptionGatewayMappingDto
> = {
  id: {
    sortable: true,
    filterable: true,
    filterClass: SubscriptionGatewayMappingIdFilter,
  },

  subscriptionGatewayId: {
    sortable: true,
    filterable: true,
    filterClass: SubscriptionGatewayIdFilter,
  },

  subscriptionId: {
    sortable: true,
    filterable: true,
    filterClass: SubscriptionIdFilter,
  },
};
