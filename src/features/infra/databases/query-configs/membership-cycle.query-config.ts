import { QueryConfig } from "@cbidigital/aqua-ddd";
import { MembershipCycleDto } from "@features/domain";
import {
  SubsMembershipCycleIdFilter,
  MembershipCycleMembershipIdFilter,
} from "@features/infra/databases/filters";

export const MembershipCycleQueryConfig: QueryConfig<keyof MembershipCycleDto> = {
  id: {
    sortable: true,
    filterable: true,
    filterClass: SubsMembershipCycleIdFilter,
  },

  membershipId: {
    sortable: true,
    filterable: true,
    filterClass: MembershipCycleMembershipIdFilter,
  },
};
