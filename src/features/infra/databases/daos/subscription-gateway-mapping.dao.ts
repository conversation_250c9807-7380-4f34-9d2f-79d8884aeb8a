import { K<PERSON> } from "knex";
import { SubscriptionGatewayMappingDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger, Optional } from "@heronjs/common";
import { SubscriptionGatewayMappingRecord } from "@features/infra/databases/records";
import { SubscriptionGatewayMappingQueryConfig } from "@features/infra/databases/query-configs";
import { SubscriptionGatewayMappingRecordMapper } from "@features/infra/databases/record-mappers";
import {
  BaseDao,
  DaoUtils,
  IBaseDao,
  IDatabase,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  MEMBERSHIP_INJECT_TOKENS,
  MEMBERSHIP_MODULE_COLUMN_NAMES,
  MEMBERSHIP_MODULE_TABLE_NAMES,
} from "@constants";

export interface ISubscriptionGatewayMappingDao
  extends IBaseDao<SubscriptionGatewayMappingDto, SubscriptionGatewayMappingRecord> {
  create(
    dto: SubscriptionGatewayMappingDto,
    options?: RepositoryOptions,
  ): Promise<SubscriptionGatewayMappingDto>;
}
@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.SUBSCRIPTION_GATEWAY_MAPPING,
  scope: Lifecycle.Singleton,
})
export class SubscriptionGatewayMappingDao
  extends BaseDao<SubscriptionGatewayMappingDto, SubscriptionGatewayMappingRecord>
  implements ISubscriptionGatewayMappingDao
{
  private readonly logger = new Logger(this.constructor.name);
  private readonly mergedColumns = [];

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.SUBSCRIPTION_GATEWAY_MAPPING,
      recordMapper: new SubscriptionGatewayMappingRecordMapper(),
    });
  }

  async create(
    dto: SubscriptionGatewayMappingDto,
    options?: RepositoryOptions,
  ): Promise<SubscriptionGatewayMappingDto> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).insert(record);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  private buildSelectClause(builder: Knex.QueryBuilder) {
    const query = builder.select("*");
    return query;
  }

  private buildJoinClause(builder: Knex.QueryBuilder) {
    builder.leftJoin(
      MEMBERSHIP_MODULE_TABLE_NAMES.SUBSCRIPTION,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.SUBSCRIPTION_GATEWAY_MAPPING}.${MEMBERSHIP_MODULE_COLUMN_NAMES.SUBSCRIPTION_GATEWAY_MAPPING.SUBSCRIPTION_ID}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.SUBSCRIPTION}.${MEMBERSHIP_MODULE_COLUMN_NAMES.SUBSCRIPTION.ID}`,
    );
  }

  async findOne(
    payload?: QueryInputFindOne<SubscriptionGatewayMappingDto>,
    options?: RepositoryOptions,
  ): Promise<Optional<Partial<SubscriptionGatewayMappingDto>>> {
    const client = this.db.getClient(options?.tenantId);
    const query = client
      .from(this.tableName)
      .modify(this.buildSelectClause)
      .modify(this.buildJoinClause);
    if (payload?.filter)
      DaoUtils.applyFilter(payload.filter, SubscriptionGatewayMappingQueryConfig, query);
    if (options?.trx) query.transacting(options.trx);
    const record = await query.first();
    if (!record) return undefined;
    return this.recordMapper.fromRecordToDto(record);
  }
}
