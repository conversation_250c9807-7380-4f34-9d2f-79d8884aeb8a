import { WebhookEventDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { WebhookEventRecord } from "@features/infra/databases/records";
import { WebhookEventRecordMapper } from "@features/infra/databases/record-mappers";
import { MEMBERSHIP_INJECT_TOKENS, MEMBERSHIP_MODULE_TABLE_NAMES } from "@constants";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";

export interface IWebhookEventDao extends IBaseDao<WebhookEventDto, WebhookEventRecord> {
  update(
    entity: Partial<WebhookEventDto>,
    options?: RepositoryOptions,
  ): Promise<Partial<WebhookEventDto>>;
}

@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.WEBHOOK_EVENT,
  scope: Lifecycle.Singleton,
})
export class WebhookEventDao
  extends BaseDao<WebhookEventDto, WebhookEventRecord>
  implements IWebhookEventDao
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.WEBHOOK_EVENT,
      recordMapper: new WebhookEventRecordMapper(),
    });
  }

  async update(
    dto: Partial<WebhookEventDto>,
    options: RepositoryOptions,
  ): Promise<Partial<WebhookEventDto>> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).where("id", dto.id!).update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }
}
