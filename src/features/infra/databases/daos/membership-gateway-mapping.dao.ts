import { Dao, DataSource, Lifecycle } from "@heronjs/common";
import { MembershipGatewayMappingDto } from "@features/domain";
import { MembershipGatewayMappingRecord } from "@features/infra/databases/records";
import { MEMBERSHIP_INJECT_TOKENS, MEMBERSHIP_MODULE_TABLE_NAMES } from "@constants";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";
import { MembershipGatewayMappingRecordMapper } from "@features/infra/databases/record-mappers";

export interface IMembershipGatewayMappingDao
  extends IBaseDao<MembershipGatewayMappingDto, MembershipGatewayMappingRecord> {
  create(
    dto: MembershipGatewayMappingDto,
    options?: RepositoryOptions,
  ): Promise<MembershipGatewayMappingDto>;
}
@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_GATEWAY_MAPPING,
  scope: Lifecycle.Singleton,
})
export class MembershipGatewayMappingDao
  extends BaseDao<MembershipGatewayMappingDto, MembershipGatewayMappingRecord>
  implements IMembershipGatewayMappingDao
{
  // private readonly logger = new Logger(this.constructor.name);
  // private readonly mergedColumns = [];

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_GATEWAY_MAPPING,
      recordMapper: new MembershipGatewayMappingRecordMapper(),
    });
  }

  async create(
    dto: MembershipGatewayMappingDto,
    options?: RepositoryOptions,
  ): Promise<MembershipGatewayMappingDto> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).insert(record);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dto;
  }
}
