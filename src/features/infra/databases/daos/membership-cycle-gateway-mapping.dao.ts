import { MembershipCycleGatewayMappingDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { MEMBERSHIP_INJECT_TOKENS, MEMBERSHIP_MODULE_TABLE_NAMES } from "@constants";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";
import { MembershipCycleGatewayMappingRecord } from "@features/infra/databases/records";
import { MembershipCycleGatewayMappingRecordMapper } from "@features/infra/databases/record-mappers";

export interface IMembershipCycleGatewayMappingDao
  extends IBaseDao<MembershipCycleGatewayMappingDto, MembershipCycleGatewayMappingRecord> {
  create(
    dto: MembershipCycleGatewayMappingDto,
    options?: RepositoryOptions,
  ): Promise<MembershipCycleGatewayMappingDto>;
}

@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CYCLE_GATEWAY_MAPPING,
  scope: Lifecycle.Singleton,
})
export class MembershipCycleGatewayMappingDao
  extends BaseDao<MembershipCycleGatewayMappingDto, MembershipCycleGatewayMappingRecord>
  implements IMembershipCycleGatewayMappingDao
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE_GATEWAY_MAPPING,
      recordMapper: new MembershipCycleGatewayMappingRecordMapper(),
    });
  }

  async create(
    dto: MembershipCycleGatewayMappingDto,
    options?: RepositoryOptions,
  ): Promise<MembershipCycleGatewayMappingDto> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).insert(record);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dto;
  }
}
