import {
  <PERSON>Dao,
  DaoUtils,
  IBaseDao,
  IDatabase,
  QueryInput,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  MEMBERSHIP_INJECT_TOKENS,
  MEMBERSHIP_MODULE_TABLE_NAMES,
  MEMBERSHIP_MODULE_COLUMN_NAMES,
} from "@constants";
import { Knex } from "knex";
import { MembershipDto } from "@features/domain";
import { MembershipRecord } from "@features/infra/databases/records";
import { Dao, DataSource, Lifecycle, Logger, Optional } from "@heronjs/common";
import { MembershipQueryConfig } from "@features/infra/databases/query-configs";
import { MembershipRecordMapper } from "@features/infra/databases/record-mappers";

export interface IMembershipDao extends IBaseDao<MembershipDto, MembershipRecord> {
  update(
    entity: Partial<MembershipDto>,
    options?: RepositoryOptions,
  ): Promise<Partial<MembershipDto>>;
  count(payload: QueryInput, options?: RepositoryOptions): Promise<number>;
}
@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP,
  scope: Lifecycle.Singleton,
})
export class MembershipDao
  extends BaseDao<MembershipDto, MembershipRecord>
  implements IMembershipDao
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP,
      recordMapper: new MembershipRecordMapper(),
    });
  }

  private buildSelectClause(builder: Knex.QueryBuilder) {
    builder.select(
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.ID}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.TENANT_ID}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.NAME}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.STATUS}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.DESCRIPTION}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.IS_CUSTOM}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.IS_TRIAL}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.CREATED_AT}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.UPDATED_AT}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.CREATED_BY}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.UPDATED_BY}`,
      builder.client.raw(
        `json_agg(${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.* ORDER BY ${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.amount)
          FILTER (WHERE ${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.id IS NOT NULL) AS cycles`,
      ),
      builder.client.raw(
        `json_agg(${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CAPABILITY}.* ORDER BY ${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CAPABILITY}.value)
          FILTER (WHERE ${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CAPABILITY}.id IS NOT NULL) AS capabilities`,
      ),
    );
  }

  private buildJoinClause(builder: Knex.QueryBuilder) {
    builder.leftJoin(
      MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CYCLE}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP_CYCLE.MEMBERSHIP_ID}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.ID}`,
    );
    builder.leftJoin(
      MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CAPABILITY,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CAPABILITY}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP_CAPABILITY.MEMBERSHIP_ID}`,
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.ID}`,
    );
  }

  private buildGroupByClause(builder: Knex.QueryBuilder) {
    builder.groupBy(
      `${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.${MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.ID}`,
    );
  }

  private applySearch(builder: Knex.QueryBuilder, search: string) {
    const query = builder.where((builder) => {
      builder.whereILike(`${MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP}.name`, `%${search}%`);
    });
    return query;
  }

  async find(payload: QueryInput, options?: RepositoryOptions): Promise<Partial<MembershipDto>[]> {
    const { offset, limit, sort, filter, search } = payload;
    const client = this.db.getClient(options?.tenantId);
    const query = client
      .from(this.tableName)
      .modify(this.buildSelectClause)
      .modify(this.buildJoinClause)
      .modify(this.buildGroupByClause);
    if (search) this.applySearch(query, search);
    if (filter) DaoUtils.applyFilter(filter, MembershipQueryConfig, query);
    if (offset !== undefined) query.offset(offset);
    if (limit !== undefined) query.limit(limit);
    if (sort) DaoUtils.applySort(sort, MembershipQueryConfig, query);
    const records = await query;
    const dtos = this.recordMapper.fromRecordsToDtos(records);
    return dtos;
  }

  async findOne(
    payload?: QueryInputFindOne<MembershipDto>,
    options?: RepositoryOptions,
  ): Promise<Optional<Partial<MembershipDto>>> {
    const client = this.db.getClient(options?.tenantId);
    const query = client
      .from(this.tableName)
      .modify(this.buildSelectClause)
      .modify(this.buildJoinClause)
      .modify(this.buildGroupByClause);
    if (payload?.filter) DaoUtils.applyFilter(payload.filter, MembershipQueryConfig, query);
    if (options?.trx) query.transacting(options.trx);
    const record = await query.first();
    return record ? this.recordMapper.fromRecordToDto(record) : undefined;
  }

  async count(payload: QueryInput, options?: RepositoryOptions): Promise<number> {
    const { filter, search } = payload;
    const client = this.db.getClient(options?.tenantId);
    const query = client.count("*").from(this.tableName);
    if (search) this.applySearch(query, search);
    if (filter) DaoUtils.applyFilter(filter, MembershipQueryConfig, query);
    const { count } = await query.first();
    return +count;
  }

  async update(
    dto: Partial<MembershipDto>,
    options: RepositoryOptions,
  ): Promise<Partial<MembershipDto>> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client
      .table(this.tableName)
      .where(MEMBERSHIP_MODULE_COLUMN_NAMES.MEMBERSHIP.ID, dto.id!)
      .update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }
}
