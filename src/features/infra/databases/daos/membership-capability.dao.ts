import { MembershipCapabilityDto } from "@features/domain";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { MembershipCapabilityRecord } from "@features/infra/databases/records";
import { MembershipCapabilityRecordMapper } from "@features/infra/databases/record-mappers";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";
import { MEMBERSHIP_INJECT_TOKENS, MEMBERSHIP_MODULE_TABLE_NAMES } from "@constants";

export interface IMembershipCapabilityDao
  extends IBaseDao<MembershipCapabilityDto, MembershipCapabilityRecord> {
  upsertList(
    dtos: Partial<MembershipCapabilityDto>[],
    options?: RepositoryOptions,
  ): Promise<Partial<MembershipCapabilityDto>[]>;
  delete(filter: Partial<MembershipCapabilityDto>, options?: RepositoryOptions): Promise<void>;
}

@Dao({
  token: MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CAPABILITY,
  scope: Lifecycle.Singleton,
})
export class MembershipCapabilityDao
  extends BaseDao<MembershipCapabilityDto, MembershipCapabilityRecord>
  implements IMembershipCapabilityDao
{
  private readonly logger = new Logger(this.constructor.name);
  private readonly mergedColumns = [
    "membership_id",
    "capability_id",
    "value",
    "created_at",
    "updated_at",
  ];

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: MEMBERSHIP_MODULE_TABLE_NAMES.MEMBERSHIP_CAPABILITY,
      recordMapper: new MembershipCapabilityRecordMapper(),
    });
  }

  async update(
    dto: Partial<MembershipCapabilityDto>,
    options: RepositoryOptions,
  ): Promise<Partial<MembershipCapabilityDto>> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).where("id", dto.id!).update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  async upsertList(dtos: Partial<MembershipCapabilityDto>[], options?: RepositoryOptions) {
    const client = this.db.getClient();
    const conflicColumns = ["id"];
    const records = this.recordMapper.fromDtosToRecords(dtos);
    const query = client
      .table(this.tableName)
      .insert(records)
      .onConflict(conflicColumns)
      .merge(this.mergedColumns);
    if (options?.trx) query.transacting(options.trx);
    await query;
    return dtos;
  }

  async delete(
    filter: Partial<MembershipCapabilityDto>,
    options?: RepositoryOptions,
  ): Promise<void> {
    const client = this.db.getClient();
    const query = client.table(this.tableName);

    if (filter.id) {
      query.where("id", filter.id);
    } else if (filter.membershipId && filter.capabilityId) {
      query.where({
        membership_id: filter.membershipId,
        capability_id: filter.capabilityId,
      });
    }

    query.delete();
    if (options?.trx) query.transacting(options.trx);
    await query;
  }
}
