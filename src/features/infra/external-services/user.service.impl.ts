import axios from "axios";
import { API_URL_CONFIG } from "@configs";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Lifecycle, Logger, Provider } from "@heronjs/common";
import { IUserProfileService, UserProfileDTO } from "@features/app";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.SERVICE.USER_PROFILE,
  scope: Lifecycle.Singleton,
})
export class UserProfileService implements IUserProfileService {
  private readonly logger = new Logger(this.constructor.name);

  async getListByIds(ids: string[]): Promise<UserProfileDTO[]> {
    try {
      const url = `${API_URL_CONFIG.USER_API_URL}/internal/users/get-by-ids`;

      const data = await axios.post(url, ids);

      // {
      //   headers: {
      //     "internal-api-key":
      //       USER_SERVICE_CONFIG.SECRET_KEY_INTERNAL_API_USER,
      //   },
      // }
      // console.dir(data, { depth: null });

      const users = data.data.data;

      return users;
    } catch (err: any) {
      this.logger.error("Failed to get users", err);
      throw err;
    }
  }
}
