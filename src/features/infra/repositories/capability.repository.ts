import {
  Inject,
  Nullable,
  Lifecycle,
  Repository as InjectRepository,
  DataSource,
} from "@heronjs/common";
import { IDatabase, QueryInput, QueryInputFindOne, RepositoryOptions } from "@cbidigital/aqua-ddd";
import {
  ICapability,
  CapabilityDto,
  ICapabilityMapper,
  ICapabilityRepository,
} from "@features/domain";
import { ICapabilityDao } from "@features/infra/databases";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";

@InjectRepository({
  token: MEMBERSHIP_INJECT_TOKENS.REPOSITORY.CAPABILITY,
  scope: Lifecycle.Singleton,
})
export class CapabilityRepository implements ICapabilityRepository {
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.CAPABILITY)
    protected readonly capabilityDao: ICapabilityDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.CAPABILITY)
    protected readonly capabilityMapper: ICapabilityMapper,
  ) {}

  async create(entity: ICapability, options?: RepositoryOptions): Promise<ICapability> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.capabilityMapper.fromEntityToDto(entity);
    await this.capabilityDao.create(dto, { ...options, trx });
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: ICapability, options?: RepositoryOptions): Promise<ICapability> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.capabilityMapper.fromEntityToDto(entity);
    await this.capabilityDao.update(dto, { ...options, trx });
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<ICapability>> {
    const dto = await this.capabilityDao.findOne(input, options);
    if (!dto) return null;

    const entity = await this.capabilityMapper.fromDtoToEntity(dto as CapabilityDto);
    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<ICapability[]> {
    const dtos = await this.capabilityDao.find(input, options);
    const entities: ICapability[] = [];

    for (const dto of dtos) {
      const entity = await this.capabilityMapper.fromDtoToEntity(dto as CapabilityDto);
      entities.push(entity);
    }

    return entities;
  }

  async count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number> {
    return this.capabilityDao.count(input, options);
  }

  async findByKey(key: string, options?: RepositoryOptions): Promise<Nullable<ICapability>> {
    const dto = await this.capabilityDao.findOne({ filter: { key: { $eq: key } } }, options);
    if (!dto) return null;

    const entity = await this.capabilityMapper.fromDtoToEntity(dto as CapabilityDto);
    return entity;
  }
}
