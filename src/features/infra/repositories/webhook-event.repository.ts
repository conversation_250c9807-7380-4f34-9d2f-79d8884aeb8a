import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { IWebhookEventDao } from "@features/infra/databases";
import { IWebhookEvent } from "@features/domain/aggregates/webhook-events";
import { Inject, Lifecycle, Repository, DataSource, Optional } from "@heronjs/common";
import { WebhookEventDto, IWebhookEventMapper, IWebhookEventRepository } from "@features/domain";
import {
  IDatabase,
  QueryInput,
  BaseRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";

@Repository({
  token: MEMBERSHIP_INJECT_TOKENS.REPOSITORY.WEBHOOK_EVENT,
  scope: Lifecycle.Singleton,
})
export class WebhookEventRepository
  extends BaseRepository<IWebhookEvent>
  implements IWebhookEventRepository
{
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.WEBHOOK_EVENT)
    protected readonly dao: IWebhookEventDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.WEBHOOK_EVENT)
    protected readonly mapper: IWebhookEventMapper,
  ) {
    super({ db });
  }

  async create(entity: IWebhookEvent, options?: RepositoryOptions): Promise<IWebhookEvent> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.dao.create(dto, { ...options, trx });
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: IWebhookEvent, options?: RepositoryOptions): Promise<IWebhookEvent> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.dao.update(dto, { ...options, trx });
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Optional<IWebhookEvent>> {
    const dto = await this.dao.findOne(input, options);

    const entity = await this.mapper.fromDtoToEntity(dto as WebhookEventDto);
    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<IWebhookEvent[]> {
    const dtos = await this.dao.find(input, options);
    const entities: IWebhookEvent[] = [];

    for (const dto of dtos) {
      const entity = await this.mapper.fromDtoToEntity(dto as WebhookEventDto);
      entities.push(entity);
    }

    return entities;
  }

  async count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number> {
    return this.dao.count(input, options);
  }
}
