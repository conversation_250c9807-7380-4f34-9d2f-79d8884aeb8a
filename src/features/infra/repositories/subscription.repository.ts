import {
  IDatabase,
  QueryInput,
  BaseRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  ISubscription,
  SubscriptionDto,
  ISubscriptionMapper,
  ISubscriptionRepository,
  ISubscriptionGatewayMapping,
  ISubscriptionGatewayMappingMapper,
} from "@features/domain";
import {
  IInvoiceDao,
  ISubscriptionDao,
  ISubscriptionGatewayMappingDao,
} from "@features/infra/databases";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Optional, Nullable, Lifecycle, Repository, DataSource } from "@heronjs/common";

@Repository({
  token: MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION,
  scope: Lifecycle.Singleton,
})
export class SubscriptionRepository
  extends BaseRepository<ISubscription>
  implements ISubscriptionRepository
{
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.SUBSCRIPTION)
    private readonly subscriptionDao: ISubscriptionDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.SUBSCRIPTION_GATEWAY_MAPPING)
    private readonly subsGatewayMappingDao: ISubscriptionGatewayMappingDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.INVOICE)
    private readonly billingCycleDao: IInvoiceDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.SUBSCRIPTION)
    protected readonly subsMapper: ISubscriptionMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.SUBSCRIPTION_GATEWAY_MAPPING)
    protected readonly subsGatewayMappingMapper: ISubscriptionGatewayMappingMapper,
  ) {
    super({ db });
  }

  async create(entity: ISubscription, options?: RepositoryOptions): Promise<ISubscription> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.subsMapper.fromEntityToDto(entity);
    await this.subscriptionDao.create(dto, { ...options, trx });
    if (dto.invoices?.length) {
      await this.billingCycleDao.createList(dto.invoices, {
        ...options,
        trx,
      });
    }
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: ISubscription, options?: RepositoryOptions): Promise<ISubscription> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.subsMapper.fromEntityToDto(entity);
    await this.subscriptionDao.update(dto, { ...options, trx });
    if (dto.invoices?.length) {
      await this.billingCycleDao.upsertList(dto.invoices, {
        ...options,
        trx,
      });
    }
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async delete(entity: ISubscription, options?: RepositoryOptions): Promise<ISubscription> {
    await this.withTransaction(async (trx) => {
      await this.subscriptionDao.deleteById(entity.id, { ...options, trx });
      return entity;
    }, options);

    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<ISubscription[]> {
    const dtos = await this.subscriptionDao.find(input, options);
    const entities = await this.subsMapper.fromDtosToEntities(dtos as SubscriptionDto[]);
    return entities;
  }

  async findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Optional<ISubscription>> {
    const dto = await this.subscriptionDao.findOne(input, options);
    const entity = dto ? await this.subsMapper.fromDtoToEntity(dto as SubscriptionDto) : dto;
    return entity;
  }

  async findOneGatewayMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<ISubscriptionGatewayMapping>> {
    const dto = await this.subsGatewayMappingDao.findOne(input, options);
    if (!dto) return null;
    const entity = await this.subsGatewayMappingMapper.fromDtoToEntity(
      dto as ISubscriptionGatewayMapping,
    );
    return entity;
  }

  async createGatewayMapping(
    entity: ISubscriptionGatewayMapping,
    options?: RepositoryOptions,
  ): Promise<ISubscriptionGatewayMapping> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.subsGatewayMappingMapper.fromEntityToDto(entity);
    await this.subsGatewayMappingDao.create(dto, {
      ...options,
      trx,
    });
    if (!options?.trx) await trx.commit();

    return entity;
  }
}
