import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Optional, Nullable, Lifecycle, Repository, DataSource } from "@heronjs/common";
import {
  IDatabase,
  QueryInput,
  BaseRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import {
  IMembership,
  MembershipDto,
  IMembershipCycle,
  IMembershipMapper,
  MembershipCycleDto,
  IMembershipRepository,
  IMembershipCycleMapper,
  IMembershipGatewayMapping,
  MembershipGatewayMappingDto,
  IMembershipCycleGatewayMapping,
  IMembershipGatewayMappingMapper,
  MembershipCycleGatewayMappingDto,
  IMembershipCycleGatewayMappingMapper,
} from "@features/domain";
import {
  IMembershipDao,
  IMembershipCycleDao,
  IMembershipCapabilityDao,
  IMembershipGatewayMappingDao,
  IMembershipCycleGatewayMappingDao,
} from "@features/infra/databases";

@Repository({
  token: MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP,
  scope: Lifecycle.Singleton,
})
export class MembershipRepository
  extends BaseRepository<IMembership>
  implements IMembershipRepository
{
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP)
    private readonly mbsDao: IMembershipDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CYCLE)
    private readonly mbsCycleDao: IMembershipCycleDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CAPABILITY)
    private readonly capabilityDao: IMembershipCapabilityDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_GATEWAY_MAPPING)
    private readonly mbsGatewayMappingDao: IMembershipGatewayMappingDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CYCLE_GATEWAY_MAPPING)
    private readonly mbsCycleGatewayMappingDao: IMembershipCycleGatewayMappingDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP)
    protected readonly mbsMapper: IMembershipMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_GATEWAY_MAPPING)
    protected readonly mbsGatewayMappingMapper: IMembershipGatewayMappingMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CYCLE_GATEWAY_MAPPING)
    protected readonly mbsCycleGatewayMappingMapper: IMembershipCycleGatewayMappingMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP_CYCLE)
    protected readonly mbsCycleMapper: IMembershipCycleMapper,
  ) {
    super({ db });
  }

  async create(entity: IMembership, options?: RepositoryOptions): Promise<IMembership> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mbsMapper.fromEntityToDto(entity);
    await this.mbsDao.create(dto, options);
    if (dto.upsertedCycles?.length) {
      await this.mbsCycleDao.createList(dto.upsertedCycles, options);
    }
    if (dto.upsertedCapabilities?.length) {
      await this.capabilityDao.createList(dto.upsertedCapabilities, options);
    }
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: IMembership, options?: RepositoryOptions): Promise<IMembership> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mbsMapper.fromEntityToDto(entity);
    await this.mbsDao.update(dto, { ...options, trx });
    const promises = [];
    if (dto.upsertedCycles?.length) {
      promises.push(this.mbsCycleDao.upsertList(dto.upsertedCycles, { ...options, trx }));
    }
    if (dto.deletedCycles?.length) {
      const deletedIds = dto.deletedCycles.map((item) => item.id);
      promises.push(this.mbsCycleDao.deleteList(deletedIds, { ...options, trx }));
    }
    if (dto.upsertedCapabilities?.length) {
      promises.push(
        this.capabilityDao.upsertList(dto.upsertedCapabilities, {
          ...options,
          trx,
        }),
      );
    }
    if (dto.deletedCapabilities?.length) {
      const deletedIds = dto.deletedCapabilities.map((item) => item.id);
      promises.push(this.capabilityDao.deleteList(deletedIds, { ...options, trx }));
    }
    await Promise.all(promises);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async delete(entity: IMembership, options?: RepositoryOptions): Promise<IMembership> {
    await this.withTransaction(async (trx) => {
      await this.mbsDao.deleteById(entity.id, { ...options, trx });
      return entity;
    }, options);

    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<IMembership[]> {
    const dtos = await this.mbsDao.find(input, options);
    const entities = await this.mbsMapper.fromDtosToEntities(dtos as MembershipDto[]);
    return entities;
  }

  async findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Optional<IMembership>> {
    const dto = await this.mbsDao.findOne(input, options);
    const entity = dto ? await this.mbsMapper.fromDtoToEntity(dto as MembershipDto) : dto;
    return entity;
  }

  async upsertList(entities: IMembership[], options: RepositoryOptions) {
    const dtos = await this.mbsMapper.fromEntitiesToDtos(entities);
    return entities;
  }

  async count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number> {
    return this.mbsDao.count(input, options);
  }

  async createGatewayMapping(
    entity: IMembershipGatewayMapping,
    options?: RepositoryOptions,
  ): Promise<IMembershipGatewayMapping> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mbsGatewayMappingMapper.fromEntityToDto(entity);
    await this.mbsGatewayMappingDao.create(dto, options);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async findOneGatewayMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipGatewayMapping>> {
    const dtos = await this.mbsGatewayMappingDao.findOne(input, options);
    if (!dtos) return null;
    const entity = await this.mbsGatewayMappingMapper.fromDtoToEntity(
      dtos as MembershipGatewayMappingDto,
    );
    return entity;
  }

  async createCycleGatewayMapping(
    entity: IMembershipCycleGatewayMapping,
    options?: RepositoryOptions,
  ): Promise<IMembershipCycleGatewayMapping> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mbsCycleGatewayMappingMapper.fromEntityToDto(entity);
    await this.mbsCycleGatewayMappingDao.create(dto, options);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async findOneCycleGatewayMapping(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipCycleGatewayMapping>> {
    const dtos = await this.mbsCycleGatewayMappingDao.findOne(input, options);
    if (!dtos) return null;
    const entity = await this.mbsCycleGatewayMappingMapper.fromDtoToEntity(
      dtos as MembershipCycleGatewayMappingDto,
    );
    return entity;
  }

  async findOneCycle(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Nullable<IMembershipCycle>> {
    const dtos = await this.mbsCycleDao.findOne(input, options);
    if (!dtos) return null;
    const entity = await this.mbsCycleMapper.fromDtoToEntity(dtos as MembershipCycleDto);
    return entity;
  }
}
