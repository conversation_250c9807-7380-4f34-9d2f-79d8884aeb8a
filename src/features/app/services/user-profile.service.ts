import { Nullable } from "@heronjs/common";

export type UserProfileDTO = {
  id: string;
  username: Nullable<string>;
  firstName: Nullable<string>;
  lastName: Nullable<string>;
  fullName: Nullable<string>;
  email: Nullable<string>;
  statusOrganization?: Nullable<string>;
  phoneCountryCode: Nullable<string>;
  phoneNumber: Nullable<string>;
  gender: Nullable<string>;
  avatar: Nullable<string>;
  dob: Nullable<Date>;
  role?: any;
};

export interface IUserProfileService {
  getListByIds(ids: string[]): Promise<UserProfileDTO[]>;
}
