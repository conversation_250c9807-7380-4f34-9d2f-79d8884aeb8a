import {
  IDatabaseUtil,
  ISubscriptionRepository,
  SubscriptioCancelledEvent,
  SubscriptionNotFoundError,
  MissingSubscriptionIdError,
} from "@features/domain";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { IWebhookHandler } from "@features/app/usecases/handlers/webhook-handler.factory";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.HANDLER.SUBSCRIPTION_CANCELLED,
  scope: Lifecycle.Singleton,
})
export class SubscriptionCancelledHandler implements IWebhookHandler {
  private readonly logger: ILogger;

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
    protected readonly subsRepo: ISubscriptionRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
  ) {
    this.logger = new Logger(this.constructor.name);
  }

  async handle(event: SubscriptioCancelledEvent) {
    const { data } = event;
    const { subscriptionGatewayId } = data;
    if (!subscriptionGatewayId) throw new MissingSubscriptionIdError();
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      const subsGatewayMapping = await this.subsRepo.findOneGatewayMapping(
        { filter: { subscriptionGatewayId: { $eq: subscriptionGatewayId } } },
        repoOptions,
      );
      if (!subsGatewayMapping) throw new SubscriptionNotFoundError();
      const subscriptionId = subsGatewayMapping.subscriptionId;
      const subscription = await this.subsRepo.findOne(
        { filter: { id: { $eq: subscriptionId } } },
        repoOptions,
      );
      if (!subscription) throw new SubscriptionNotFoundError();
      await subscription.cancel();
      await this.subsRepo.update(subscription, repoOptions);
      await trx.rollback();
    } catch (error) {
      await trx.rollback();
      this.logger.error("Failed to handle subscription cancelled", error);
      throw error;
    }
  }
}
