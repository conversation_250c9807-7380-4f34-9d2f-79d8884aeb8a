import {
  IDatabaseUtil,
  ISubscription,
  IPaymentGateway,
  InvoiceStatusEnum,
  SubscriptionGateway,
  IMembershipRepository,
  PaymentSucceededEvent,
  IPaymentGatewayFactory,
  ISubscriptionRepository,
  SubscriptionNotFoundError,
  MissingSubscriptionIdError,
} from "@features/domain";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { RepositoryOptions } from "@cbidigital/aqua-ddd";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { IWebhookHandler } from "@features/app/usecases/handlers/webhook-handler.factory";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.HANDLER.PAYMENT_SUCCEEDED,
  scope: Lifecycle.Singleton,
})
export class PaymentSucceededHandler implements IWebhookHandler {
  private readonly logger: ILogger;
  private paymentGateway: IPaymentGateway;

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
    protected readonly subscriptionRepo: ISubscriptionRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly membershipRepo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
  ) {
    this.logger = new Logger(this.constructor.name);
  }

  async handle(event: PaymentSucceededEvent) {
    const { data } = event;
    // const invoiceId = data.invoice;
    const { subscriptionId } = data.metadata;
    if (!subscriptionId) throw new MissingSubscriptionIdError();
    this.logger.info("Handled payment succeeded for:::", subscriptionId);
    this.paymentGateway = this.paymentGatewayFactory.get(event.gateway);
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      // Handle subscription created
      const { subscription, subscriptionGateway } = await this.fetchSubscription(
        event,
        repoOptions,
      );
      await this.updateBillingCycle(subscription, subscriptionGateway);
      await subscription.activate();
      await this.subscriptionRepo.update(subscription, repoOptions);
      await trx.commit();
    } catch (error) {
      await trx.rollback();
      this.logger.error("Failed to handle payment succeeded", error);
      throw error;
    }
  }

  private async fetchSubscription(event: PaymentSucceededEvent, repoOptions: RepositoryOptions) {
    const { subscriptionId } = event.data.metadata;
    const [subscription, subscriptionGateway] = await Promise.all([
      this.subscriptionRepo.findOne({ filter: { id: { $eq: subscriptionId } } }, repoOptions),
      this.paymentGateway.getSubscription(event.data.subscription),
    ]);
    if (!subscription || !subscriptionGateway) throw new SubscriptionNotFoundError();
    return { subscription, subscriptionGateway };
  }

  private async updateBillingCycle(
    subscription: ISubscription,
    subscriptionGateway: SubscriptionGateway,
  ) {
    const pendingInvoice = subscription.getPendingInvoice();
    const { periodStart, periodEnd } = subscriptionGateway;
    if (pendingInvoice) {
      await pendingInvoice.update({
        periodStart,
        periodEnd,
        status: InvoiceStatusEnum.SUCCEEDED,
      });
    }
  }
}
