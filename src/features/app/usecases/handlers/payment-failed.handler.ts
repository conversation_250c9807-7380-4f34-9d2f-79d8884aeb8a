import {
  IDatabaseUtil,
  InvoiceStatusEnum,
  IMembershipRepository,
  PaymentSucceededEvent,
  IPaymentGatewayFactory,
  ISubscriptionRepository,
  SubscriptionNotFoundError,
  MissingSubscriptionIdError,
} from "@features/domain";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { IWebhookHand<PERSON> } from "@features/app/usecases/handlers/webhook-handler.factory";

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.HANDLER.PAYMENT_FAILED,
  scope: Lifecycle.Singleton,
})
export class PaymentFailedHandler implements IWebhookHandler {
  private readonly logger: ILogger;

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
    protected readonly subscriptionRepo: ISubscriptionRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly membershipRepo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
  ) {
    this.logger = new Logger(this.constructor.name);
  }

  async handle(event: PaymentSucceededEvent) {
    const { data } = event;
    // const invoiceGatewayId = data.invoice;
    const { subscriptionId } = data.metadata;
    if (!subscriptionId) throw new MissingSubscriptionIdError();
    this.logger.info("Handled payment failed for:::", subscriptionId);
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      // Handle subscription created
      const subscription = await this.subscriptionRepo.findOne(
        { filter: { id: { $eq: subscriptionId } } },
        repoOptions,
      );
      if (!subscription) throw new SubscriptionNotFoundError();
      const invoice = subscription.getPendingInvoice();
      if (invoice)
        await invoice.update({
          status: InvoiceStatusEnum.FAILED,
        });

      await subscription.onPaymentFailed();
      await this.subscriptionRepo.update(subscription, repoOptions);
      await trx.commit();
    } catch (error) {
      await trx.rollback();
      this.logger.error("Failed to handle payment succeeded", error);
      throw error;
    }
  }
}
