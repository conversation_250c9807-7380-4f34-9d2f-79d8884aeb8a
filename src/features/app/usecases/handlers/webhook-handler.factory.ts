import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_EVENT_TYPE, PaymentEvent } from "@features/domain";

export interface IWebhookHandler {
  handle(event: PaymentEvent): Promise<void>;
}

export interface IWebhookHandlerFactory {
  get(type: string): IWebhookHandler;
}

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.FACTORY.WEBHOOK_HANDLER,
  scope: Lifecycle.Singleton,
})
export class WebhookHandlerFactory implements IWebhookHandlerFactory {
  private readonly handlerMap: Array<{
    types: string[];
    handler: IWebhookHandler;
  }>;
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.HANDLER.PAYMENT_FAILED)
    private readonly paymentFailedHandler: IWebhook<PERSON>and<PERSON>,
    @Inject(MEMBERSHIP_INJECT_TOKENS.HANDLER.PAYMENT_SUCCEEDED)
    private readonly paymentSucceededHandler: IWebhookHandler,
    @Inject(MEMBERSHIP_INJECT_TOKENS.HANDLER.INVOICE_CREATED)
    private readonly invoiceCreatedHandler: IWebhookHandler,
    @Inject(MEMBERSHIP_INJECT_TOKENS.HANDLER.SUBSCRIPTION_CANCELLED)
    private readonly subscriptionCancelledHandler: IWebhookHandler,
  ) {
    this.handlerMap = [
      {
        types: PAYMENT_EVENT_TYPE.PAYMENT_SUCCEEDED,
        handler: this.paymentSucceededHandler,
      },
      {
        types: PAYMENT_EVENT_TYPE.SUBSCRIPTION_CANCELLED,
        handler: this.subscriptionCancelledHandler,
      },
      {
        types: PAYMENT_EVENT_TYPE.PAYMENT_FAILED,
        handler: this.paymentFailedHandler,
      },
      {
        types: PAYMENT_EVENT_TYPE.INVOICE_CREATED,
        handler: this.invoiceCreatedHandler,
      },
    ];
  }

  get(type: string) {
    let handler = null;
    for (const entry of this.handlerMap) {
      if (entry.types.includes(type)) {
        handler = entry.handler;
        break;
      }
    }
    if (!handler) throw new Error("Webhook handler not found");
    return handler;
  }
}
