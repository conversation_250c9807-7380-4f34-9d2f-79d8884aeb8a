import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { IPaymentService } from "@features/app/services";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { UseCase, IUseCase, UseCaseContext, RepositoryOptions } from "@cbidigital/aqua-ddd";
import {
  IDatabaseUtil,
  ISubscription,
  IPaymentGateway,
  ISubscriptionBuilder,
  CustomerNotFoundError,
  IMembershipRepository,
  PaymentGatewayProvider,
  IPaymentGatewayFactory,
  ISubscriptionRepository,
  PaymentGatewaySubscription,
  SubscriptionGatewayMapping,
  CustomerIdIsNotProvidedError,
  MembershipCycleNotFoundError,
} from "@features/domain";

export type ActivateSubscriptionUseCaseInput = {
  gateway: PaymentGatewayProvider;
  membershipId: string;
  membershipCycleId: string;
};

export type ActivateSubscriptionUseCaseOutput = void;

const ActivateSubscriptionUseCaseInputSchema = z.object({
  gateway: z.nativeEnum(PaymentGatewayProvider),
  membershipId: z.string(),
  membershipCycleId: z.string(),
});

export type IActivateSubscriptionUseCase = IUseCase<
  ActivateSubscriptionUseCaseInput,
  ActivateSubscriptionUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.ACTIVATE_SUBSCRIPTION,
  scope: Lifecycle.Transient,
})
export class ActivateSubscriptionUseCase
  extends UseCase<
    ActivateSubscriptionUseCaseInput,
    ActivateSubscriptionUseCaseOutput,
    UseCaseContext
  >
  implements IActivateSubscriptionUseCase
{
  private paymentGateway: IPaymentGateway;
  private readonly logger: ILogger;
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.SUBSCRIPTION)
    protected readonly builder: ISubscriptionBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly membershipRepo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.SERVICE.PAYMENT)
    protected readonly paymentService: IPaymentService,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
    protected readonly subscriptionRepo: ISubscriptionRepository,
  ) {
    super();
    this.setMethods(this.processing);
    this.logger = new Logger(this.constructor.name);
  }

  processing = async (input: ActivateSubscriptionUseCaseInput) => {
    const model = ActivateSubscriptionUseCaseInputSchema.parse(input);
    const { gateway } = model;
    this.paymentGateway = this.paymentGatewayFactory.get(gateway);
    const customerId = this.context.auth?.authId;
    if (!customerId) throw new CustomerIdIsNotProvidedError();
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      const [subs, customerInfo] = await Promise.all([
        this.getSubscription(model, repoOptions),
        this.paymentService.getCustomerGateway(customerId),
      ]);
      if (!customerInfo) throw new CustomerNotFoundError();
      const membershipCycleGatewayMapping = await this.getMembershipCycleGatewayMapping(
        model,
        repoOptions,
      );

      // Create subscription
      const subsGateway = await this.paymentGateway.createSubscription({
        customerId: customerInfo.gatewayCustomerId,
        priceId: membershipCycleGatewayMapping.membershipCycleGatewayId,
        metadata: { tenantMembershipId: subs.id },
      });

      // Create tenant membership mapping
      await this.createSubsGatewayMapping(
        { subs, subsGateway, customerInfo, gateway },
        repoOptions,
      );
      await trx.commit();
      this.logger.info("Subscription activated", subsGateway);
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  };

  private async getSubscription(
    input: ActivateSubscriptionUseCaseInput,
    repoOptions: RepositoryOptions,
  ) {
    let subs = await this.subscriptionRepo.findOne(
      {
        filter: {
          tenantId: { $eq: this.context.tenantId },
          membershipId: { $eq: input.membershipId },
        },
      },
      repoOptions,
    );
    if (!subs) {
      subs = await this.builder.build();
      await subs.create({
        gateway: input.gateway,
        tenantId: this.context.tenantId!,
        membershipId: input.membershipId,
        membershipCycleId: input.membershipCycleId,
        createdBy: this.context.auth!.authId!,
      });
      await this.subscriptionRepo.create(subs, repoOptions);
    }

    return subs;
  }

  private async getMembershipCycleGatewayMapping(
    input: ActivateSubscriptionUseCaseInput,
    repoOptions: RepositoryOptions,
  ) {
    const membershipCycle = await this.membershipRepo.findOneCycle(
      {
        filter: {
          id: { $eq: input.membershipCycleId },
          membershipId: { $eq: input.membershipId },
        },
      },
      repoOptions,
    );
    if (!membershipCycle) throw new MembershipCycleNotFoundError();
    const membershipCycleGatewayMapping = membershipCycle.getGatewayMapping(input.gateway);
    if (!membershipCycleGatewayMapping) throw new MembershipCycleNotFoundError();
    return membershipCycleGatewayMapping;
  }

  private createSubsGatewayMapping = async (
    input: {
      subs: ISubscription;
      subsGateway: PaymentGatewaySubscription;
      customerInfo: any;
      gateway: PaymentGatewayProvider;
    },
    repoOptions: RepositoryOptions,
  ) => {
    const subsGatewayMapping = new SubscriptionGatewayMapping();
    await subsGatewayMapping.create({
      gateway: input.gateway,
      subscriptionId: input.subs.id,
      subscriptionGatewayId: input.subsGateway.id,
    });
    await this.subscriptionRepo.createGatewayMapping(subsGatewayMapping, repoOptions);
  };
}
