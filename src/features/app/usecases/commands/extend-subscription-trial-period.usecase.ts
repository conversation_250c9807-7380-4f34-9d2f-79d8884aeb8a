import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import {
  ISubscriptionBuilder,
  ISubscriptionRepository,
  SubscriptionNotFoundError,
} from "@features/domain";

export type ExtendSubscriptionTrialPeriodUseCaseInput = {
  subscriptionId: string;
  trialPeriodDays: number;
};

export type ExtendSubscriptionTrialPeriodUseCaseOutput = void;

const ExtendSubscriptionTrialPeriodUseCaseInputSchema = z.object({
  trialPeriodDays: z.number(),
  subscriptionId: z.string(),
});

export type IExtendSubscriptionTrialPeriodUseCase = IUseCase<
  ExtendSubscriptionTrialPeriodUseCaseInput,
  ExtendSubscriptionTrialPeriodUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.EXTEND_SUBSCRIPTION_TRIAL_PERIOD,
  scope: Lifecycle.Transient,
})
export class ExtendSubscriptionTrialPeriodUseCase
  extends UseCase<
    ExtendSubscriptionTrialPeriodUseCaseInput,
    ExtendSubscriptionTrialPeriodUseCaseOutput,
    UseCaseContext
  >
  implements IExtendSubscriptionTrialPeriodUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.SUBSCRIPTION)
    protected readonly builder: ISubscriptionBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
    protected readonly subscriptionRepo: ISubscriptionRepository,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: ExtendSubscriptionTrialPeriodUseCaseInput) => {
    const model = ExtendSubscriptionTrialPeriodUseCaseInputSchema.parse(input);
    const currSubs = await this.subscriptionRepo.findOne({
      filter: {
        id: { $eq: model.subscriptionId },
        isActive: { $eq: true },
      },
    });
    if (!currSubs) throw new SubscriptionNotFoundError();
    await currSubs.extendTrialPeriod(model.trialPeriodDays);
    await this.subscriptionRepo.update(currSubs);
  };
}
