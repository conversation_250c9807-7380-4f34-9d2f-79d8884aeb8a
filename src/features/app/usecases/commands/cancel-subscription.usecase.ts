import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import {
  IPaymentGateway,
  IPaymentGatewayFactory,
  ISubscriptionRepository,
  SubscriptionNotFoundError,
} from "@features/domain";

export type CancelSubscriptionUseCaseInput = { subscriptionId: string };
export type CancelSubscriptionUseCaseOutput = { id: string };

const CancelSubscriptionUseCaseInputSchema = z.object({
  subscriptionId: z.string(),
});

export type ICancelSubscriptionUseCase = IUseCase<
  CancelSubscriptionUseCaseInput,
  CancelSubscriptionUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.CANCEL_SUBSCRIPTION,
  scope: Lifecycle.Transient,
})
export class CancelSubscriptionUseCase
  extends UseCase<CancelSubscriptionUseCaseInput, CancelSubscriptionUseCaseOutput, UseCaseContext>
  implements ICancelSubscriptionUseCase
{
  private readonly logger: ILogger;
  private paymentGateway: IPaymentGateway;
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
    protected readonly repo: ISubscriptionRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
  ) {
    super();
    this.setMethods(this.processing);
    this.logger = new Logger(this.constructor.name);
  }

  processing = async (input: CancelSubscriptionUseCaseInput) => {
    const model = CancelSubscriptionUseCaseInputSchema.parse(input);
    try {
      const subs = await this.repo.findOne({
        filter: {
          id: { $eq: model.subscriptionId },
          isActive: { $eq: true },
        },
      });
      if (!subs) throw new SubscriptionNotFoundError();
      this.paymentGateway = this.paymentGatewayFactory.get(subs.gateway!);
      if (!this.paymentGateway) throw new Error("Payment gateway not found");
      const subsGatewayMapping = subs.getSubscriptionGatewayMapping(subs.gateway!);
      if (!subsGatewayMapping) throw new SubscriptionNotFoundError();
      await this.paymentGateway.cancelSubscription({
        subscriptionId: subsGatewayMapping.subscriptionGatewayId,
      });
      return { id: subs.id };
    } catch (error) {
      this.logger.error("Failed to cancel subscription", error);
      throw error;
    }
  };
}
