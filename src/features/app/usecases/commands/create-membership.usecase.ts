import z from "zod";
import { PAYMENT_GATEWAY_CONFIG } from "@configs";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { PAYMENT_CONFIG } from "src/configs/payment.config";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { UseCase, IUseCase, UseCaseContext, RepositoryOptions } from "@cbidigital/aqua-ddd";
import {
  IMembership,
  IDatabaseUtil,
  IPaymentGateway,
  IMembershipBuilder,
  MembershipCycleEnum,
  PaymentGatewayProduct,
  CreateMembershipInput,
  IMembershipRepository,
  IPaymentGatewayFactory,
  MembershipGatewayMapping,
  MembershipCycleGatewayMapping,
} from "@features/domain";

export type CreateMembershipUseCaseInput = CreateMembershipInput;
export type CreateMembershipUseCaseOutput = { id: string };

const CreateMembershipUseCaseInputSchema = z.object({
  tenantId: z.string().nullable().default(null),
  name: z.string(),
  isCustom: z.boolean(),
  isTrial: z.boolean(),
  description: z.string().optional(),
  cycles: z
    .array(
      z.object({
        amount: z.number().positive(),
        billingCycle: z.nativeEnum(MembershipCycleEnum),
      }),
    )
    .optional(),
  capabilities: z
    .array(
      z.object({
        capabilityId: z.string(),
        value: z.string(),
      }),
    )
    .optional(),
});

export type ICreateMembershipUseCase = IUseCase<
  CreateMembershipUseCaseInput,
  CreateMembershipUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.CREATE_MEMBERSHIP,
  scope: Lifecycle.Transient,
})
export class CreateMembershipUseCase
  extends UseCase<CreateMembershipUseCaseInput, CreateMembershipUseCaseOutput, UseCaseContext>
  implements ICreateMembershipUseCase
{
  private readonly paymentGateway: IPaymentGateway;
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.MEMBERSHIP)
    protected readonly builder: IMembershipBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly repo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
    @Inject(MEMBERSHIP_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
  ) {
    super();
    this.setMethods(this.processing);
    this.paymentGateway = this.paymentGatewayFactory.get(PAYMENT_GATEWAY_CONFIG.PROVIDER);
  }

  processing = async (input: CreateMembershipUseCaseInput) => {
    const context = this.context as { auth: { authId: string } };
    const model = CreateMembershipUseCaseInputSchema.parse(input);
    const entity = await this.builder.build();
    await entity.create(model, context);
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      const product = await this.createProduct(entity);
      await this.repo.create(entity, repoOptions);
      await this.createMembershipGatewayMapping(entity, product, repoOptions);
      await this.createPrices(entity, product, repoOptions);
      await trx.commit();
      return { id: entity.id };
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  };

  private createProduct = async (entity: IMembership) => {
    const product = await this.paymentGateway.createProduct({
      name: entity.name,
      description: entity.description,
      metadata: {
        membershipId: entity.id,
        tenantId: entity.tenantId,
      },
    });
    return product;
  };

  private createMembershipGatewayMapping = async (
    entity: IMembership,
    product: PaymentGatewayProduct,
    repoOptions: RepositoryOptions,
  ) => {
    const membershipGatewayMapping = new MembershipGatewayMapping();
    await membershipGatewayMapping.create({
      membershipId: entity.id,
      membershipGatewayId: product.id,
      gateway: PAYMENT_GATEWAY_CONFIG.PROVIDER,
    });
    await this.repo.createGatewayMapping(membershipGatewayMapping, repoOptions);
    return membershipGatewayMapping;
  };

  private getInterval = (billingCycle: MembershipCycleEnum) => {
    if (billingCycle === MembershipCycleEnum.MONTHLY) return "month";
    if (billingCycle === MembershipCycleEnum.YEARLY) return "year";
    return "month";
  };

  private usdToCents = (amount: number) => amount * 100;

  private createPrices = async (
    entity: IMembership,
    product: PaymentGatewayProduct,
    repoOptions: RepositoryOptions,
  ) => {
    const promises = entity.upsertedCycles?.map(async (cycle) => {
      const price = await this.paymentGateway.createPrice({
        productId: product.id,
        amount: this.usdToCents(cycle.amount),
        currency: "USD",
        recurring: {
          interval: this.getInterval(cycle.billingCycle),
          intervalCount: PAYMENT_CONFIG.INTERVAL_COUNT,
        },
        metadata: {
          membershipId: entity.id,
          membershipCycleId: cycle.id,
        },
      });
      const membershipCycleGatewayMapping = new MembershipCycleGatewayMapping();
      await membershipCycleGatewayMapping.create({
        membershipCycleId: cycle.id,
        membershipCycleGatewayId: price.id,
        gateway: PAYMENT_GATEWAY_CONFIG.PROVIDER,
      });
      await this.repo.createCycleGatewayMapping(membershipCycleGatewayMapping, repoOptions);
    });
    if (promises) await Promise.all(promises);
  };
}
