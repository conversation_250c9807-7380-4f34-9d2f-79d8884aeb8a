import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider, Logger } from "@heronjs/common";
import { UseCase, IUseCase, UseCaseContext, RepositoryOptions } from "@cbidigital/aqua-ddd";
import {
  IDatabaseUtil,
  ISubscription,
  ISubscriptionBuilder,
  SubscriptionStatusEnum,
  ISubscriptionRepository,
  CreateSubscriptionInput,
  MissingTrialPeriodDaysError,
  ActiveMembershipExistsError,
} from "@features/domain";

export type AssignSubscriptionUseCaseInput = CreateSubscriptionInput;

export type AssignSubscriptionUseCaseOutput = { id: string };

const AssignSubscriptionUseCaseInputSchema = z.object({
  tenantId: z.string(),
  membershipId: z.string(),
  customerId: z.string().optional(),
  membershipCycleId: z.string(),
  isTrial: z.boolean().optional(),
  trialPeriodDays: z.number().optional(),
  createdBy: z.string(),
});

export type IAssignSubscriptionUseCase = IUseCase<
  AssignSubscriptionUseCaseInput,
  AssignSubscriptionUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.ASSIGN_SUBSCRIPTION,
  scope: Lifecycle.Transient,
})
export class AssignSubscriptionUseCase
  extends UseCase<AssignSubscriptionUseCaseInput, AssignSubscriptionUseCaseOutput, UseCaseContext>
  implements IAssignSubscriptionUseCase
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.BUILDER.SUBSCRIPTION)
    protected readonly builder: ISubscriptionBuilder,
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.SUBSCRIPTION)
    protected readonly subsRepo: ISubscriptionRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: AssignSubscriptionUseCaseInput) => {
    const model = AssignSubscriptionUseCaseInputSchema.parse(input);
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };

    try {
      // Handle current subscription
      await this.handleCurrentSubscription(model, repoOptions);

      // Create new subscription
      const entity = await this.createSubscription(model, repoOptions);
      await trx.commit();
      return { id: entity.id };
    } catch (error) {
      this.logger.error("Failed to assign subscription", error);
      await trx.rollback();
      throw error;
    }
  };

  /**
   * Handle current subscription
   */
  private async handleCurrentSubscription(
    model: AssignSubscriptionUseCaseInput,
    repoOptions: RepositoryOptions,
  ): Promise<void> {
    const currentSubs = await this.subsRepo.findOne(
      {
        filter: {
          tenantId: { $eq: model.tenantId },
          status: {
            $is: [SubscriptionStatusEnum.ACTIVE, SubscriptionStatusEnum.TRIALING],
          },
        },
      },
      repoOptions,
    );

    if (currentSubs) throw new ActiveMembershipExistsError();
  }

  /**
   * Create a new subscription
   */
  private async createSubscription(
    model: AssignSubscriptionUseCaseInput,
    repoOptions: RepositoryOptions,
  ): Promise<ISubscription> {
    // Create subscription
    const isMissingTrialPeriodDays = model.isTrial && !model.trialPeriodDays;
    if (isMissingTrialPeriodDays) throw new MissingTrialPeriodDaysError();
    const entity = await this.builder.build();
    await entity.create(model);

    // Save subscription
    await this.subsRepo.create(entity, repoOptions);

    return entity;
  }
}
