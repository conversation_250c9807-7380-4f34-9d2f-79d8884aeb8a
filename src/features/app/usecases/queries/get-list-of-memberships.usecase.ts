import { UseCase, IUseCase, ResultOf, UseCaseContext, PaginationInput } from "@cbidigital/aqua-ddd";
import { IMembershipDao } from "@features/infra";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { IUserProfileService } from "@features/app/services";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { MembershipDto, IMembershipMapper } from "@features/domain";

export type GetListOfMembershipsUseCaseInput = PaginationInput;

export type GetListOfMembershipsUseCaseOutput = {
  totalCount: number;
  memberships: Partial<MembershipDto>[];
};

export type IGetListOfMembershipsUseCase = IUseCase<
  GetListOfMembershipsUseCaseInput,
  GetListOfMembershipsUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.GET_LIST_OF_MEMBERSHIPS,
  scope: Lifecycle.Transient,
})
export class GetListOfMembershipsUseCase
  extends UseCase<
    GetListOfMembershipsUseCaseInput,
    GetListOfMembershipsUseCaseOutput,
    UseCaseContext
  >
  implements IGetListOfMembershipsUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP)
    protected readonly dao: IMembershipDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP)
    protected readonly mapper: IMembershipMapper,
    @Inject(MEMBERSHIP_INJECT_TOKENS.SERVICE.USER_PROFILE)
    protected readonly userService: IUserProfileService,
  ) {
    super();
    this.setMethods(this.processing, this.map);
  }

  processing = async (input: GetListOfMembershipsUseCaseInput) => {
    const { filter } = input;

    const [memberships, totalCount] = await Promise.all([
      this.dao.find(input),
      this.dao.count({ filter }),
    ]);

    return { memberships, totalCount };
  };

  map = async (input: ResultOf<GetListOfMembershipsUseCase, "processing">) => {
    const { memberships, totalCount } = input;
    const userIds = memberships
      .map((item) => [item.createdBy, item.updatedBy])
      .flat()
      .filter((item) => item != null);
    const uniqueUserIds = [...new Set(userIds)];
    const users = await this.userService.getListByIds(uniqueUserIds);
    const userMap = new Map(users.map((user) => [user.id, user]));
    memberships.forEach((membership) => {
      const createdBy = userMap.get(membership.createdBy!);
      membership.createdByName = `${createdBy?.firstName} ${createdBy?.lastName}`;
      if (membership.updatedBy) {
        const updatedBy = userMap.get(membership.updatedBy);
        membership.updatedByName = `${updatedBy?.firstName} ${updatedBy?.lastName}`;
      }
    });
    return { totalCount, memberships };
  };
}
