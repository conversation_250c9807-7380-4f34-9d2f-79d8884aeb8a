import z from "zod";
import { IMem<PERSON>hipDao } from "@features/infra";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { MembershipDto, IMembershipMapper, MembershipNotFoundError } from "@features/domain";

export type GetMembershipByIdUseCaseInput = { id: string };

export type GetMembershipByIdUseCaseOutput = MembershipDto;

const GetMembershipByIdUseCaseInputSchema = z.object({
  id: z.string(),
});

export type IGetMembershipByIdUseCase = IUseCase<
  GetMembershipByIdUseCaseInput,
  GetMembershipByIdUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.GET_MEMBERSHIP_BY_ID,
  scope: Lifecycle.Transient,
})
export class GetMembershipByIdUseCase
  extends UseCase<GetMembershipByIdUseCaseInput, GetMembershipByIdUseCaseOutput, UseCaseContext>
  implements IGetMembershipByIdUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP)
    protected readonly dao: IMembershipDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.MEMBERSHIP)
    protected readonly mapper: IMembershipMapper,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: GetMembershipByIdUseCaseInput) => {
    const { id } = GetMembershipByIdUseCaseInputSchema.parse(input);
    const dto = await this.dao.findOne({ filter: { id: { $eq: id } } });
    if (!dto) throw new MembershipNotFoundError();

    return dto;
  };
}
