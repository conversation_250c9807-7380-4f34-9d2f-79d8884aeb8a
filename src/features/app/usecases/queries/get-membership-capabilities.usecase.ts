import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import {
  MembershipCapabilityDto,
  MembershipNotFoundError,
  IMembershipRepository,
} from "@features/domain";
import { IMembershipCapabilityDao } from "@features/infra/databases";

export type GetMembershipCapabilitiesUseCaseInput = {
  membershipId: string;
};

export type GetMembershipCapabilitiesUseCaseOutput = {
  capabilities: MembershipCapabilityDto[];
};

const GetMembershipCapabilitiesUseCaseInputSchema = z.object({
  membershipId: z.string(),
});

export type IGetMembershipCapabilitiesUseCase = IUseCase<
  GetMembershipCapabilitiesUseCaseInput,
  GetMembershipCapabilitiesUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.GET_MEMBERSHIP_CAPABILITIES,
  scope: Lifecycle.Transient,
})
export class GetMembershipCapabilitiesUseCase
  extends UseCase<
    GetMembershipCapabilitiesUseCaseInput,
    GetMembershipCapabilitiesUseCaseOutput,
    UseCaseContext
  >
  implements IGetMembershipCapabilitiesUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.REPOSITORY.MEMBERSHIP)
    protected readonly membershipRepo: IMembershipRepository,
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.MEMBERSHIP_CAPABILITY)
    protected readonly membershipCapabilityDao: IMembershipCapabilityDao,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: GetMembershipCapabilitiesUseCaseInput) => {
    const { membershipId } = GetMembershipCapabilitiesUseCaseInputSchema.parse(input);

    // Verify membership exists
    const membership = await this.membershipRepo.findOne({
      filter: { id: { $eq: membershipId } },
    });
    if (!membership) {
      throw new MembershipNotFoundError();
    }

    // Get all capabilities for the membership
    const capabilities = await this.membershipCapabilityDao.find({
      filter: { membershipId: { $eq: membershipId } },
    });

    return { capabilities };
  };
}
