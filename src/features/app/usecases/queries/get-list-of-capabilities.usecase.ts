import { UseCase, IUseCase, UseCaseContext, PaginationInput } from "@cbidigital/aqua-ddd";
import { CapabilityDto } from "@features/domain";
import { ICapabilityDao } from "@features/infra";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";

export type GetListOfCapabilitiesUseCaseInput = PaginationInput;

export type GetListOfCapabilitiesUseCaseOutput = {
  capabilities: CapabilityDto[];
  totalCount: number;
};

export type IGetListOfCapabilitiesUseCase = IUseCase<
  GetListOfCapabilitiesUseCaseInput,
  GetListOfCapabilitiesUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.GET_LIST_OF_CAPABILITIES,
  scope: Lifecycle.Transient,
})
export class GetListOfCapabilitiesUseCase
  extends UseCase<
    GetListOfCapabilitiesUseCaseInput,
    GetListOfCapabilitiesUseCaseOutput,
    UseCaseContext
  >
  implements IGetListOfCapabilitiesUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.CAPABILITY)
    protected readonly dao: ICapabilityDao,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: GetListOfCapabilitiesUseCaseInput) => {
    const { filter } = input;

    const [capabilities, totalCount] = await Promise.all([
      this.dao.find(input),
      this.dao.count({ filter }),
    ]);

    return { capabilities, totalCount };
  };
}
