import { ISubscriptionDao } from "@features/infra";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { SubscriptionDto, ISubscriptionMapper, SubscriptionNotFoundError } from "@features/domain";

export type GetSubscriptionUseCaseInput = { id: string };

export type GetSubscriptionUseCaseOutput = SubscriptionDto;

export type IGetSubscriptionUseCase = IUseCase<
  GetSubscriptionUseCaseInput,
  GetSubscriptionUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.GET_SUBSCRIPTION,
  scope: Lifecycle.Transient,
})
export class GetSubscriptionUseCase
  extends UseCase<GetSubscriptionUseCaseInput, GetSubscriptionUseCaseOutput, UseCaseContext>
  implements IGetSubscriptionUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.SUBSCRIPTION)
    protected readonly dao: ISubscriptionDao,
    @Inject(MEMBERSHIP_INJECT_TOKENS.MAPPER.SUBSCRIPTION)
    protected readonly mapper: ISubscriptionMapper,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: GetSubscriptionUseCaseInput) => {
    const dto = await this.dao.findOne({ filter: { id: { $eq: input.id } } });
    if (!dto) throw new SubscriptionNotFoundError();

    return dto;
  };
}
