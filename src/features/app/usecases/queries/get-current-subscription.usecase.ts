import z from "zod";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { ISubscriptionDao } from "@features/infra";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { SubscriptionDto, subscriptionStatusOrder } from "@features/domain";

export type GetCurrentSubscriptionUseCaseInput = {
  tenantId: string;
};

export type GetCurrentSubscriptionUseCaseOutput = SubscriptionDto;

const GetCurrentSubscriptionUseCaseInputSchema = z.object({
  tenantId: z.string(),
});

export type IGetCurrentSubscriptionUseCase = IUseCase<
  GetCurrentSubscriptionUseCaseInput,
  GetCurrentSubscriptionUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MEMBERSHIP_INJECT_TOKENS.USECASE.GET_CURRENT_SUBSCRIPTION,
  scope: Lifecycle.Transient,
})
export class GetCurrentSubscriptionUseCase
  extends UseCase<
    GetCurrentSubscriptionUseCaseInput,
    GetCurrentSubscriptionUseCaseOutput,
    UseCaseContext
  >
  implements IGetCurrentSubscriptionUseCase
{
  constructor(
    @Inject(MEMBERSHIP_INJECT_TOKENS.DAO.SUBSCRIPTION)
    protected readonly dao: ISubscriptionDao,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: GetCurrentSubscriptionUseCaseInput) => {
    const { tenantId } = GetCurrentSubscriptionUseCaseInputSchema.parse(input);

    // Find active subscription for the current tenant
    const dtos = await this.dao.find({
      filter: { tenantId: { $eq: tenantId } },
    });
    const [dto] = dtos.sort((a, b) => {
      const aStatus = subscriptionStatusOrder[a.status!];
      const bStatus = subscriptionStatusOrder[b.status!];
      return aStatus - bStatus;
    });

    return { data: dto ?? null };
  };
}
