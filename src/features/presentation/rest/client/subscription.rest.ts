import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Get, Rest, Body, Fuse, Post, Guard, Param, Principal } from "@heronjs/common";
import {
  ICancelSubscriptionUseCase,
  IActivateSubscriptionUseCase,
  IGetCurrentSubscriptionUseCase,
  ActivateSubscriptionUseCaseInput,
} from "@features/app";

@Rest("/subscriptions")
export class SubscriptionRest {
  @Get({ uri: "/current" })
  @Guard({ private: true })
  async getCurrent(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.GET_CURRENT_SUBSCRIPTION)
    useCase: IGetCurrentSubscriptionUseCase,
    @Principal("sub") authId: string,
    @Principal("organization") tenantId: string,
  ) {
    const result = await useCase.exec({ tenantId }, { auth: { authId } });
    return result;
  }

  @Post({ uri: "/activate", code: 201 })
  @Guard({ private: true })
  async activate(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.ACTIVATE_SUBSCRIPTION)
    useCase: IActivateSubscriptionUseCase,
    @Principal("sub") authId: string,
    @Principal("organization") tenantId: string,
    @Body() body: ActivateSubscriptionUseCaseInput,
  ) {
    const result = await useCase.exec(body, { auth: { authId }, tenantId });
    return result;
  }

  @Post({ uri: "/:id/cancel", code: 201 })
  @Guard({ private: true })
  async cancel(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.CANCEL_SUBSCRIPTION)
    useCase: ICancelSubscriptionUseCase,
    @Principal("sub") authId: string,
    @Param("id") subscriptionId: string,
  ) {
    const result = await useCase.exec({ subscriptionId }, { auth: { authId } });
    return result;
  }
}
