import { HttpRequest } from "@heronjs/express";
import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { PaymentGatewayProvider } from "@features/domain";
import { IHandleWebhookEventUseCase } from "@features/app";
import { Body, Fuse, Param, Post, Rest, Request } from "@heronjs/common";

@Rest("/webhook/:provider")
export class PaymentWebhook {
  constructor() {}

  @Post({ uri: "/", code: 201 })
  async handle(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.HANDLE_WEBHOOK_EVENT)
    useCase: IHandleWebhookEventUseCase,
    @Param("provider") provider: PaymentGatewayProvider,
    @Body() body: any,
    @Request() req: HttpRequest,
  ) {
    const headers = req.headers as Record<string, string>;
    const result = await useCase.exec({ body, gateway: provider, headers }, {});
    return result;
  }
}
