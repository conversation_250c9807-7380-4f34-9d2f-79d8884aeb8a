import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { Rest, Body, Fuse, <PERSON>, <PERSON>, Param, Principal } from "@heronjs/common";
import {
  IAssignSubscriptionUseCase,
  AssignSubscriptionUseCaseInput,
  IExtendSubscriptionTrialPeriodUseCase,
  ExtendSubscriptionTrialPeriodUseCaseInput,
} from "@features/app";

@Rest("/admin/subscriptions")
export class AdminSubscriptionRest {
  @Post({ uri: "/", code: 201 })
  @Guard({ private: true })
  async assign(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.ASSIGN_SUBSCRIPTION)
    useCase: IAssignSubscriptionUseCase,
    @Principal("sub") authId: string,
    @Body() body: AssignSubscriptionUseCaseInput,
  ) {
    const result = await useCase.exec({ ...body, createdBy: authId }, { auth: { authId } });
    return result;
  }

  @Post({ uri: "/:id/extend-trial", code: 204 })
  @Guard({ private: true })
  async extendTrial(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.EXTEND_SUBSCRIPTION_TRIAL_PERIOD)
    useCase: IExtendSubscriptionTrialPeriodUseCase,
    @Param("id") subscriptionId: string,
    @Principal("sub") authId: string,
    @Body() body: ExtendSubscriptionTrialPeriodUseCaseInput,
  ) {
    const result = await useCase.exec({ ...body, subscriptionId }, { auth: { authId } });
    return result;
  }
}
