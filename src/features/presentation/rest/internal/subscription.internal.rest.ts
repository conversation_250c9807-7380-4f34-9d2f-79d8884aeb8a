import { MEMBERSHIP_INJECT_TOKENS } from "@constants";
import { IGetSubscriptionUseCase } from "@features/app";
import { InternalApiKeyInterceptor } from "@interceptors";
import { Get, Rest, Fuse, Param, UseInterceptors } from "@heronjs/common";

@Rest("/internal/subscriptions")
export class InternalSubscriptionRest {
  @Get({ uri: "/:id" })
  @UseInterceptors([InternalApiKeyInterceptor])
  async getById(
    @Fuse(MEMBERSHIP_INJECT_TOKENS.USECASE.GET_SUBSCRIPTION)
    useCase: IGetSubscriptionUseCase,
    @Param("id") id: string,
  ) {
    return useCase.exec({ id });
  }
}
